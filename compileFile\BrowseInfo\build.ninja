##IAR Ninja build file


#Rules
rule COMPILER_XCL
  command = D$:\software\iar\common\bin\XclFileGenerator.exe $xclcommand -f "$rspfile_name"
  description = IAR_NEW_TOOL+++COMPILER_XCL+++$out
  rspfile = $rspfile_name
  rspfile_content = $flags

rule INDEXER
  command = D$:\software\iar\common\bin\SourceIndexer.exe $flags
  depfile = $out.dep
  deps = gcc
  description = IAR_NEW_TOOL+++INDEXER+++$out

rule MAKEBROWSE
  command = D$:\software\iar\common\bin\makeBrowseData.exe $flags
  description = IAR_NEW_TOOL+++MAKEBROWSE+++$out

rule PDBLINK
  command = D$:\software\iar\common\bin\PbdLink.exe $flags
  description = IAR_NEW_TOOL+++PDBLINK+++$out



#Build steps
build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\led.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\led.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\light_fn_18089876452921840385.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\led.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\led.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\led.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\led.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\led.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\light.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\light.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\light_fn_18089876452921840385.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\light.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\light.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\light.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\light.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\light.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\lsd.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\lsd.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\light_fn_18089876452921840385.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\lsd.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\lsd.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\lsd.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\lsd.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\lsd.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\sfud\sfud.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\sfud_6715763213700909193.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\sfud\sfud.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_port.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\sfud\sfud_port.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\sfud_6715763213700909193.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_port.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_port.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\sfud\sfud_port.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_port.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_port.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_sfdp.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\sfud\sfud_sfdp.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\sfud_6715763213700909193.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_sfdp.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_sfdp.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\sfud\sfud_sfdp.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_sfdp.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_sfdp.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\animal.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\animal.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\animal.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\animal.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\animal.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\animal.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\animal.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\dataSerialization.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\dataSerialization.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\dataSerialization.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\dataSerialization.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\dataSerialization.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\dataSerialization.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\dataSerialization.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\debugApp.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\debugApp.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\debugApp.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\debugApp.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\debugApp.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\debugApp.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\debugApp.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\print.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\print.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\print.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\print.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\print.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\print.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\print.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\ringbuffer.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\ringbuffer.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\ringbuffer.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\ringbuffer.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\ringbuffer.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\ringbuffer.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\ringbuffer.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\spi_nor_time_series.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\spi_nor_time_series.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\spi_nor_time_series.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\spi_nor_time_series.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\spi_nor_time_series.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\spi_nor_time_series.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\spi_nor_time_series.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\structAndFuc.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\structAndFuc.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\structAndFuc.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\structAndFuc.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\structAndFuc.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\structAndFuc.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\structAndFuc.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\sysTickApp.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\sysTickApp.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\sysTickApp.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\sysTickApp.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\sysTickApp.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\sysTickApp.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\sysTickApp.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\uplinkEsp8266App.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\uplinkEsp8266App.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\uplinkEsp8266App.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\uplinkEsp8266App.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\uplinkEsp8266App.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\uplinkEsp8266App.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\uplinkEsp8266App.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\userRtos.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\userRtos.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\userRtos.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\userRtos.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\userRtos.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\userRtos.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\userRtos.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\W25Q128_flash.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\W25Q128_flash.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\W25Q128_flash.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\W25Q128_flash.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\W25Q128_flash.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\W25Q128_flash.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\W25Q128_flash.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\dma.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\dma.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\dma.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\dma.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\dma.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\dma.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\dma.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\freertos.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\freertos.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\freertos.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\freertos.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\freertos.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\freertos.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\freertos.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\gpio.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\gpio.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\gpio.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\gpio.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\gpio.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\gpio.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\gpio.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\main.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\main.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\main.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\main.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\main.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\main.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\main.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\spi.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\spi.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\spi.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\spi.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\spi.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\spi.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\spi.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\stm32f1xx_hal_msp.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\stm32f1xx_hal_msp.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\stm32f1xx_it.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\stm32f1xx_it.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\usart.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\usart.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\usart.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\usart.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\usart.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\usart.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\usart.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\system_stm32f1xx.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\CMSIS_6603591812247902717.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\system_stm32f1xx.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_spi.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_spi.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\backUartMid.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\mid\backUartMid.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\mid_1628371642073116930.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\backUartMid.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\backUartMid.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\mid\backUartMid.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\backUartMid.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\backUartMid.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\debugUartMid.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\mid\debugUartMid.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\mid_1628371642073116930.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\debugUartMid.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\debugUartMid.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\mid\debugUartMid.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\debugUartMid.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\debugUartMid.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\uplinkUartMid.xcl : COMPILER_XCL 
    flags = D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\mid\uplinkUartMid.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\mid_1628371642073116930.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\uplinkUartMid.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\uplinkUartMid.xcl.rsp
    xclcommand = -source_file D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\mid\uplinkUartMid.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\uplinkUartMid.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\uplinkUartMid.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\cmsis_os2.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\cmsis_os2.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\cmsis_os2.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\cmsis_os2.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\cmsis_os2.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\croutine.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\croutine.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\croutine.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\croutine.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\croutine.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\croutine.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\croutine.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\event_groups.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\event_groups.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\event_groups.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\event_groups.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\event_groups.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\event_groups.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\event_groups.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\heap_4.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\MemMang\heap_4.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\heap_4.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\heap_4.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\MemMang\heap_4.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\heap_4.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\heap_4.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\list.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\list.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\list.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\list.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\list.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\list.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\list.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\port.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\IAR\ARM_CM3\port.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\port.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\port.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\IAR\ARM_CM3\port.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\port.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\port.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\queue.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\queue.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\queue.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\queue.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\queue.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\queue.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\queue.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\stream_buffer.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\stream_buffer.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\stream_buffer.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\stream_buffer.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\stream_buffer.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\stream_buffer.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\stream_buffer.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\tasks.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\tasks.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\tasks.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\tasks.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\tasks.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\tasks.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\tasks.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\timers.xcl : COMPILER_XCL 
    flags = C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\timers.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../Core/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/STM32F1xx_HAL_Driver/Inc\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Middlewares/Third_Party/FreeRTOS/Source/portable/IAR/ARM_CM3\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F1_V1.8.4/Drivers/CMSIS/Include\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../app/sfud\ -I D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/../mid\ -On --predef_macros D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\timers.tmp
    rspfile_name = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\timers.xcl.rsp
    xclcommand = -source_file C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\timers.c -xcl_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\timers.xcl -macro_file D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\timers.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\led.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\led.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\led.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\led.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\led.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\light.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\light.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\light.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\light.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\light.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\lsd.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\lsd.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\lsd.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\lsd.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\lsd.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\sfud\sfud.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_port.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_port.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\sfud\sfud_port.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_port.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_port.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_sfdp.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_sfdp.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\sfud\sfud_sfdp.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_sfdp.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_sfdp.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\animal.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\animal.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\animal.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\animal.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\animal.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\dataSerialization.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\dataSerialization.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\dataSerialization.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\dataSerialization.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\dataSerialization.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\debugApp.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\debugApp.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\debugApp.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\debugApp.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\debugApp.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\print.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\print.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\print.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\print.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\print.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\ringbuffer.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\ringbuffer.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\ringbuffer.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\ringbuffer.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\ringbuffer.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\spi_nor_time_series.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\spi_nor_time_series.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\spi_nor_time_series.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\spi_nor_time_series.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\spi_nor_time_series.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\structAndFuc.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\structAndFuc.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\structAndFuc.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\structAndFuc.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\structAndFuc.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\sysTickApp.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\sysTickApp.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\sysTickApp.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\sysTickApp.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\sysTickApp.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\uplinkEsp8266App.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\uplinkEsp8266App.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\uplinkEsp8266App.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\uplinkEsp8266App.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\uplinkEsp8266App.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\userRtos.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\userRtos.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\userRtos.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\userRtos.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\userRtos.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\W25Q128_flash.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\W25Q128_flash.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\app\W25Q128_flash.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\W25Q128_flash.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\W25Q128_flash.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\dma.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\dma.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\dma.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\dma.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\dma.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\freertos.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\freertos.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\freertos.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\freertos.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\freertos.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\gpio.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\gpio.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\gpio.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\gpio.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\gpio.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\main.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\main.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\main.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\main.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\main.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\spi.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\spi.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\spi.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\spi.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\spi.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\stm32f1xx_hal_msp.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\stm32f1xx_it.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\usart.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\usart.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\usart.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\usart.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\usart.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\system_stm32f1xx.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_spi.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\backUartMid.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\backUartMid.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\mid\backUartMid.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\backUartMid.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\backUartMid.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\debugUartMid.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\debugUartMid.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\mid\debugUartMid.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\debugUartMid.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\debugUartMid.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\uplinkUartMid.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\uplinkUartMid.xcl | D$:\routine\stm32f1_software\software_All_example\TEST_userSoftware\mid\uplinkUartMid.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\uplinkUartMid.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\uplinkUartMid.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\cmsis_os2.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\cmsis_os2.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\cmsis_os2.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\cmsis_os2.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\croutine.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\croutine.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\croutine.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\croutine.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\croutine.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\event_groups.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\event_groups.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\event_groups.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\event_groups.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\event_groups.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\heap_4.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\heap_4.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\MemMang\heap_4.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\heap_4.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\heap_4.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\list.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\list.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\list.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\list.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\list.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\port.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\port.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\IAR\ARM_CM3\port.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\port.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\port.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\queue.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\queue.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\queue.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\queue.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\queue.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\stream_buffer.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\stream_buffer.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\stream_buffer.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\stream_buffer.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\stream_buffer.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\tasks.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\tasks.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\tasks.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\tasks.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\tasks.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\timers.pbi : INDEXER D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\timers.xcl | C$:\Users\Administrator\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\timers.c
    flags = -out=D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\timers.pbi -f D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\timers.xcl

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader.pbw : MAKEBROWSE D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader.pbd
    flags = D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader.pbd -output D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader.pbw

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part0.pbi : PDBLINK D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\led.pbi | D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\light.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\lsd.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_port.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_sfdp.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\animal.pbi
    flags = -M D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part0.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\led.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\light.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\light_fn_18089876452921840385.dir\lsd.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_port.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\sfud_6715763213700909193.dir\sfud_sfdp.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\animal.pbi

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part1.pbi : PDBLINK D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\dataSerialization.pbi | D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\debugApp.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\print.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\ringbuffer.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\spi_nor_time_series.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\structAndFuc.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\sysTickApp.pbi
    flags = -M D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part1.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\dataSerialization.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\debugApp.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\print.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\ringbuffer.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\spi_nor_time_series.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\structAndFuc.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\sysTickApp.pbi

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part2.pbi : PDBLINK D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\uplinkEsp8266App.pbi | D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\userRtos.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\W25Q128_flash.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\dma.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\freertos.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\gpio.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\main.pbi
    flags = -M D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part2.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\uplinkEsp8266App.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\userRtos.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\app_1052054763364153231.dir\W25Q128_flash.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\dma.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\freertos.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\gpio.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\main.pbi

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part3.pbi : PDBLINK D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\spi.pbi | D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\usart.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.pbi
    flags = -M D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part3.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\spi.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\usart.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.pbi

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part4.pbi : PDBLINK D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.pbi | D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.pbi
    flags = -M D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part4.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.pbi

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part5.pbi : PDBLINK D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.pbi | D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\backUartMid.pbi
    flags = -M D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part5.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\backUartMid.pbi

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part6.pbi : PDBLINK D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\debugUartMid.pbi | D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\uplinkUartMid.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\cmsis_os2.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\croutine.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\event_groups.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\heap_4.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\list.pbi
    flags = -M D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part6.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\debugUartMid.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\mid_1628371642073116930.dir\uplinkUartMid.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\cmsis_os2.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\croutine.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\event_groups.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\heap_4.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\list.pbi

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part7.pbi : PDBLINK D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\port.pbi | D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\queue.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\stream_buffer.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\tasks.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\timers.pbi
    flags = -M D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part7.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\port.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\queue.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\stream_buffer.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\tasks.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\FreeRTOS_4809373609813369194.dir\timers.pbi

build D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader.pbd : PDBLINK D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part0.pbi | D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part1.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part2.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part3.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part4.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part5.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part6.pbi $
 D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part7.pbi
    flags = -M D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader.pbd D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part0.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part1.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part2.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part3.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part4.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part5.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part6.pbi D$:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\TEST_bootloader_part7.pbi

