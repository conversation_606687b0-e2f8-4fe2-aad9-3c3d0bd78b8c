/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2023 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f1xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define IAPtx_Pin GPIO_PIN_2
#define IAPtx_GPIO_Port GPIOA
#define IAPrx_Pin GPIO_PIN_3
#define IAPrx_GPIO_Port GPIOA
#define W25_CS_Pin GPIO_PIN_12
#define W25_CS_GPIO_Port GPIOB
#define W25_SCK_Pin GPIO_PIN_13
#define W25_SCK_GPIO_Port GPIOB
#define W25_MI_Pin GPIO_PIN_14
#define W25_MI_GPIO_Port GPIOB
#define W25_MO_Pin GPIO_PIN_15
#define W25_MO_GPIO_Port GPIOB
#define upLinkTx_Pin GPIO_PIN_8
#define upLinkTx_GPIO_Port GPIOD
#define upLinkRx_Pin GPIO_PIN_9
#define upLinkRx_GPIO_Port GPIOD
#define deBugTx_Pin GPIO_PIN_9
#define deBugTx_GPIO_Port GPIOA
#define deBugRx_Pin GPIO_PIN_10
#define deBugRx_GPIO_Port GPIOA
#define _24c02_SCL_Pin GPIO_PIN_6
#define _24c02_SCL_GPIO_Port GPIOB
#define _24c02_SDA_Pin GPIO_PIN_7
#define _24c02_SDA_GPIO_Port GPIOB
/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
