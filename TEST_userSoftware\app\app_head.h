#ifndef __APP_HEAD_H
#define __APP_HEAD_H

#if defined(__ICCARM__)

#pragma diag_suppress = Pe177
#pragma diag_suppress = Pe550
#pragma diag_suppress = Pe1

#else

#pragma diag_suppress 177
#pragma diag_suppress 550
#pragma diag_suppress 1

#endif

#include "FreeRTOS.h"
#include "task.h"
#include "queue.h"
#include "userRtos.h"
#include "stdint.h"
#include "ringbuffer.h"
#include "debugApp.h"
#include "animal.h"
#include "print.h"
#include "string.h"
#include "sfud.h"
#include "stdarg.h"
#include "dataSerialization.h"
#include "structAndFuc.h"
#include "light.h"
#include "led.h"
#include "lsd.h"

#define DEBUG_HANDLE (&huart2)
#define USER_MALLOC pvPortMalloc
#define USER_FREE vPortFree

typedef struct
{
    uint8_t recvSysTickCnt;
    uint16_t recvIndex;
    uint8_t *recvBufPtr;

    uint16_t sendIndex;
    uint8_t *sendBufPtr;

} uartCtrlType_t;

#endif