/*
 * spi_nor_time_series.c
 *
 * Template: Append-only time-series storage on SPI NOR Flash
 * - 15-min slot records (configurable)
 * - 256B payload per record (configurable)
 * - Append-only data area + append-only index area
 * - GC (sector reclaim), index rebuild on boot, drop-power safe
 *
 * NOTE: This is a template / reference implementation. You must
 * implement the HAL functions for your platform (SPI flash read/write/erase)
 * and adapt constants (SECTOR_SIZE_4K, PAGE_SIZE, FLASH_BASE, etc.).
 *
 * Key invariants:
 * - Records are appended to Data Area. Each record has a header with
 *   slot, seq, timestamp, flags and crc16.
 * - Index entries are appended to Index Area after record is fully
 *   written and validated. On boot we scan index area to rebuild
 *   an in-RAM map (slot -> latest record addr).
 * - G<PERSON> picks sectors with low live-record ratio, migrates live records
 *   and erases sector.
 */
#include "spi_nor_time_series.h"
#include "W25Q128_flash.h"
#include <stdbool.h>
#include <string.h>

/* ------------------------------ Types ------------------------------------ */
#pragma pack(push, 1)
typedef struct {
    uint32_t slot;      // 槽
    uint32_t seq;       // 序列号
    uint32_t timestamp; // 时间戳
    uint16_t flags;     // 标志
    uint16_t crc16;     // CRC
} rec_hdr_t;            // 用户数据
#pragma pack(pop)

/* ----------------------------- Configuration ------------------------------ */
#define FLASH_BASE_ADDR 0x00000000UL       // 逻辑分区的基地址
#define FLASH_TOTAL_SIZE (1024 * 1024 * 4) // 4MB分区

typedef char compile_time_check_flash_size[(FLASH_TOTAL_SIZE <= W25Q_TOTAL_SIZE) ? 1 : -1];

#if FLASH_TOTAL_SIZE > W25Q_TOTAL_SIZE
#error "FLASH_TOTAL_SIZE is too large"
#endif

#define SECTOR_SIZE_4K W25Q_SECTOR_SIZE // 典型的NOR扇区（擦除单位）
#define PAGE_SIZE W25Q_PAGE_SIZE        // 典型的编程页面

#define RECORD_HDR_SIZE (sizeof(rec_hdr_t))                       // 我们将打包一个16B的头部
#define RECORD_PAYLOAD_SIZE (W25Q_PAGE_SIZE - RECORD_HDR_SIZE)    // 用户数据
#define RECORD_TOTAL_SIZE (RECORD_HDR_SIZE + RECORD_PAYLOAD_SIZE) // 总记录大小
#define RECORDS_PER_SECTOR (SECTOR_SIZE_4K / RECORD_TOTAL_SIZE)   // 每个扇区的记录数

#define DATA_AREA_BASE FLASH_BASE_ADDR                                         // 数据区域从逻辑分区开始
#define DATA_AREA_SIZE (FLASH_TOTAL_SIZE - (DATA_AREA_BASE - FLASH_BASE_ADDR)) // 数据区域大小为分区剩余空间

// Slot = timestamp / SLOT_SECONDS
#define SLOT_SECONDS 300U // 5 minutes

#define FLAG_WRITING 0x01
#define FLAG_VALID 0x02

/* ------------------------ HAL (platform must implement) ------------------- */

#define HAL_FLASH_READ(addr, buf, len) W25Q_Flash->read(addr, buf, len)
#define HAL_FLASH_PROGRAM(addr, buf, len) W25Q_Flash->write(addr, (const uint8_t*)buf, len)
#define HAL_FLASH_ERASE_SECTOR(addr) W25Q_Flash->erase_4k(addr)

// For debugging/time we expose a logger; you can replace with your own
#ifndef FS_LOG
extern int user_printf(const char* fmt, ...);
#define FS_LOG(...) user_printf(__VA_ARGS__)
#endif

/* --------------------------- Utility functions --------------------------- */
// Simple CRC16-CCITT (0x1021) - small and portable
static uint16_t crc16_ccitt(const uint8_t* data, uint32_t len, uint16_t seed) {
    uint16_t crc = seed;
    for (uint32_t i = 0; i < len; ++i) {
        crc ^= (uint16_t)data[i] << 8;
        for (int j = 0; j < 8; ++j) {
            if (crc & 0x8000)
                crc = (crc << 1) ^ 0x1021;
            else
                crc <<= 1;
        }
    }
    return crc;
}

/* --------------------------- In-memory state ----------------------------- */
#define SLOT_MAP_CAPACITY 1024U // tunable: max number of distinct slots cached

typedef struct {
    uint32_t slot;  // 0 means empty (we avoid slot==0 by offsetting if needed)
    uint32_t addr;  // flash addr
} slot_map_entry_t; //

static slot_map_entry_t slot_map[SLOT_MAP_CAPACITY];
static uint16_t slot_valid = 0;
static uint32_t data_write_ptr = DATA_AREA_BASE;

/* --------------------------- Low-level helpers --------------------------- */
static inline uint32_t align_up(uint32_t v, uint32_t a) {
    return (v + a - 1u) / a * a;
}

// Knuth乘法哈希: 2654435761 = 2^32 * (√5-1)/2 (黄金分割点)
#define HASH_MARK (SLOT_MAP_CAPACITY - 1)
#define SLOT_MAP_HASH(x) ((x * 2654435761u) & HASH_MARK)

/**
 * @description: 初始化slot_map哈希表，将所有条目清零（标记为空）
 */
static void slot_map_init(void) {
    memset(slot_map, 0, sizeof(slot_map));
}

/**
 * @description:
 * 查找指定slot在哈希表中的索引位置，使用线性探测法解决哈希冲突，遇到空槽位表示查找失败，放入的时候就算有数据也是往后顺延，所以得到0的话，肯定是不存在这个slot
 * @param {uint32_t} slot: 要查找的槽位号
 * @return {int} 成功返回索引位置，失败返回-1（未找到或表空）
 *
 * 实现说明:
 * 1. 使用Knuth乘法哈希算法计算初始索引位置
 * 2. 采用线性探测法解决哈希冲突
 * 3. 遇到空槽位(slot==0)表示查找失败，放入的时候就算有数据也是往后顺延，所以得到0的话，肯定是不存在这个slot
 */
static int slot_map_find_hash(uint32_t slot) {
    uint32_t idx = SLOT_MAP_HASH(slot);

    for (uint32_t i = 0; i < SLOT_MAP_CAPACITY; ++i) {
        uint32_t p = (idx + i) % SLOT_MAP_CAPACITY;

        if (slot_map[p].slot == 0)
            return -1;

        if (slot_map[p].slot == slot)
            return (int)p;
    }
    return -1;
}

/**
 * @description: 插入或更新slot映射条目
 * @param {uint32_t} slot: 槽位号
 * @param {uint32_t} addr: 对应的闪存物理地址
 * @param {uint32_t} seq: 序列号
 * @return {int} 成功返回存储位置索引，失败返回-1（哈希表已满）
 *
 * 实现说明:
 * 1. 如果slot不存在则寻找空槽位插入，如果存在则更新槽位信息
 * 2. 使用与查找相同的哈希算法和冲突解决策略
 */
static int slot_map_put(uint32_t slot, uint32_t addr) {
    uint32_t idx = SLOT_MAP_HASH(slot);
    for (uint32_t i = 0; i < SLOT_MAP_CAPACITY; ++i) {
        uint32_t p = (idx + i) % SLOT_MAP_CAPACITY;

        if (slot_map[p].slot == 0 || slot_map[p].slot == slot) {
            slot_map[p].slot = slot;
            slot_map[p].addr = addr;
            slot_valid++;
            return (int)p;
        }
    }
    return -1; // 哈希表已满，无法插入
}

/**
 * @description: 获取指定slot的地址和序列号
 * @param {uint32_t} slot: 要查询的槽位号
 * @param {uint32_t*} addr_out: 输出参数-闪存地址
 * @return {int} 成功返回0，失败返回-1（未找到）
 */
static int slot_map_get(uint32_t slot, uint32_t* addr_out) {
    // 先查找slot在哈希表中的位置
    int idx = slot_map_find_hash(slot);
    if (idx < 0)
        return -1;

    if (addr_out)
        *addr_out = slot_map[idx].addr;

    return 0;
}

static int slot_map_del(uint32_t slot) {
    int idx = slot_map_find_hash(slot);
    if (idx < 0)
        return -1;

    slot_map[idx].slot = 0;
    slot_map[idx].addr = 0;
    slot_valid--;
    return 0;
}

static void slot_map_del_min_slot(void) {
    int slot_idx = 0;

    for (size_t i = 0; i < SLOT_MAP_CAPACITY; i++) {
        slot_idx = slot_map[i].slot < slot_map[slot_idx].slot ? i : slot_idx;
    }

    memset(&slot_map[slot_idx], 0, sizeof(slot_map_entry_t));
    slot_valid--;
}

/* --------------------------- Record write/read --------------------------- */
// Write a single record atomically (append-only)
static int fs_write_record(uint32_t timestamp, const void* payload) {
    // 写入的时候确保扇区是RECORD_TOTAL_SIZE的倍数
    data_write_ptr =
        ((data_write_ptr + RECORD_TOTAL_SIZE > DATA_AREA_SIZE + DATA_AREA_BASE) ? DATA_AREA_BASE
                                                                                : data_write_ptr + RECORD_TOTAL_SIZE);

    uint8_t _buf[RECORD_TOTAL_SIZE] = {0};
    rec_hdr_t* hdr = (rec_hdr_t*)_buf;

    // data_write_ptr不是扇区开头的话，肯定是不需要擦除的
    if ((data_write_ptr % SECTOR_SIZE_4K) == 0) {
        HAL_FLASH_ERASE_SECTOR(data_write_ptr);
    }
    uint32_t slot = timestamp / SLOT_SECONDS;
    hdr->slot = slot;
    hdr->flags = FLAG_VALID;
    hdr->crc16 = crc16_ccitt(payload, RECORD_PAYLOAD_SIZE, 0xFFFF);
    memcpy(_buf + RECORD_HDR_SIZE, payload, RECORD_PAYLOAD_SIZE);
    HAL_FLASH_PROGRAM(data_write_ptr, _buf, RECORD_TOTAL_SIZE);

    slot_map_del_min_slot();
    if (slot_map_put(slot, data_write_ptr))
        return -1;

    return 0;
}

// Read record by timestamp (fast path uses in-RAM slot map)
static int fs_read_by_timestamp(uint32_t timestamp, void* payload_out) {
    uint32_t slot = timestamp / SLOT_SECONDS;
    uint32_t addr = 0;
    if (slot_map_get(slot, &addr) == 0) {
        // read header + payload
        uint8_t buf[RECORD_TOTAL_SIZE];
        if (HAL_FLASH_READ(addr, buf, RECORD_TOTAL_SIZE) != 0)
            return -1;
        rec_hdr_t hdr;
        memcpy(&hdr, buf, RECORD_HDR_SIZE);
        // verify flags and crc
        if (!(hdr.flags & FLAG_VALID))
            return -2; // not valid
        uint16_t calc = crc16_ccitt(buf + RECORD_HDR_SIZE, RECORD_PAYLOAD_SIZE, 0xFFFF);
        uint16_t stored = (uint16_t)buf[14] | ((uint16_t)buf[15] << 8);
        if (calc != stored)
            return -3; // crc fail
        memcpy(payload_out, buf + RECORD_HDR_SIZE, RECORD_PAYLOAD_SIZE);
        return 0;
    }
    return -4; // not found
}

/* --------------------------- Index rebuild on boot ----------------------- */
// 顺序扫描索引区域，重建槽映射并设置写指针
static int rebuild_index(void) {
    FS_LOG("Rebuilding index...");
    slot_map_init();
    FS_LOG("Index rebuild done. data_ptr=0x%08X", data_write_ptr);
    return 0;
}

/* --------------------------- Public API / init --------------------------- */
int fs_init(void) {
    // rebuild index & set pointers
    if (rebuild_index() != 0)
        return -1;
    // load global_seq_counter from meta area if desired
    return 0;
}

// user-facing write
int fs_store_timestamped(uint32_t timestamp, const void* payload) {
    return fs_write_record(timestamp, payload);
}

// user-facing read
int fs_load_by_timestamp(uint32_t timestamp, void* payload_out) {
    return fs_read_by_timestamp(timestamp, payload_out);
}
