/*
 * spi_nor_time_series.c
 *
 * Template: Append-only time-series storage on SPI NOR Flash
 * - 15-min slot records (configurable)
 * - 256B payload per record (configurable)
 * - Append-only data area + append-only index area
 * - GC (sector reclaim), index rebuild on boot, drop-power safe
 *
 * NOTE: This is a template / reference implementation. You must
 * implement the HAL functions for your platform (SPI flash read/write/erase)
 * and adapt constants (SECTOR_SIZE, PAGE_SIZE, FLASH_BASE, etc.).
 *
 * Key invariants:
 * - Records are appended to Data Area. Each record has a header with
 *   slot, seq, timestamp, flags and crc16.
 * - Index entries are appended to Index Area after record is fully
 *   written and validated. On boot we scan index area to rebuild
 *   an in-RAM map (slot -> latest record addr).
 * - <PERSON><PERSON> picks sectors with low live-record ratio, migrates live records
 *   and erases sector.
 */
#include <string.h>
#include <stdbool.h>
#include "W25Q128_flash.h"
#include "spi_nor_time_series.h"

/* ----------------------------- Configuration ------------------------------ */
#define FLASH_BASE_ADDR 0x00000000UL         // 逻辑分区的基地址
#define FLASH_TOTAL_SIZE W25Q_TOTAL_SIZE / 2 // 16MB分区
#define SECTOR_SIZE W25Q_SECTOR_SIZE         // 典型的NOR扇区（擦除单位）
#define PAGE_SIZE W25Q_PAGE_SIZE             // 典型的编程页面

#define RECORD_PAYLOAD_SIZE 256U // 用户数据
#define RECORD_HDR_SIZE 16U      // 我们将打包一个16B的头部
#define RECORD_TOTAL_SIZE (RECORD_HDR_SIZE + RECORD_PAYLOAD_SIZE)

#define RECORDS_PER_SECTOR (SECTOR_SIZE / RECORD_TOTAL_SIZE)

// 分区布局： [META][INDEX...][DATA...]
#define META_SECTORS 1U                                        // 元数据扇区（签名版本信息）
#define INDEX_AREA_SIZE (64 * 1024UL)                          // 64KB索引区域（根据需要调整）
#define INDEX_ENTRY_SIZE 16U                                   // slot(4) addr(4) seq(4) crc16(2) reserved(2)
#define INDEX_ENTRIES_MAX (INDEX_AREA_SIZE / INDEX_ENTRY_SIZE) // 索引条目最大数量

#define INDEX_AREA_BASE (FLASH_BASE_ADDR + (META_SECTORS * SECTOR_SIZE))
#define DATA_AREA_BASE (INDEX_AREA_BASE + INDEX_AREA_SIZE)
#define DATA_AREA_SIZE (FLASH_TOTAL_SIZE - (DATA_AREA_BASE - FLASH_BASE_ADDR))

// Slot = timestamp / SLOT_SECONDS
#define SLOT_SECONDS 300U // 5 minutes

#define FLAG_WRITING 0x01
#define FLAG_VALID 0x02

/* ------------------------------ Types ------------------------------------ */
#pragma pack(push, 1)
typedef struct {
    uint32_t slot;      // 槽
    uint32_t seq;       // 序列
    uint32_t timestamp; // 时间戳
    uint16_t flags;     // 标志
    uint16_t crc16;     // CRC
} rec_hdr_t;            // 用户数据
#pragma pack(pop)

#pragma pack(push, 1)
typedef struct {
    uint32_t slot;  // 槽
    uint32_t addr;  // 物理地址（开始的头部）
    uint32_t seq;   // 序列
    uint16_t crc16; // CRC 对于这个索引条目（槽+地址+seq）
    uint16_t _pad;  // 保留
} index_entry_t;    // 索引
#pragma pack(pop)

/* ------------------------ HAL (platform must implement) ------------------- */

#define HAL_FLASH_READ(addr, buf, len) W25Q_Flash->read(addr, buf, len)
#define HAL_FLASH_PROGRAM(addr, buf, len) W25Q_Flash->write(addr, (const uint8_t*)buf, len)
#define HAL_FLASH_ERASE_SECTOR(addr) W25Q_Flash->erase_4k(addr)

// For debugging/time we expose a logger; you can replace with your own
#ifndef FS_LOG
extern int user_printf(const char *fmt, ...);
#define FS_LOG(...) user_printf(__VA_ARGS__)
#endif

/* --------------------------- Utility functions --------------------------- */
// Simple CRC16-CCITT (0x1021) - small and portable
static uint16_t crc16_ccitt(const uint8_t* data, uint32_t len, uint16_t seed) {
    uint16_t crc = seed;
    for (uint32_t i = 0; i < len; ++i) {
        crc ^= (uint16_t)data[i] << 8;
        for (int j = 0; j < 8; ++j) {
            if (crc & 0x8000)
                crc = (crc << 1) ^ 0x1021;
            else
                crc <<= 1;
        }
    }
    return crc;
}

/* --------------------------- In-memory state ----------------------------- */
/*
 * In-memory state:
 * - slot_map: slot -> record
 * - global_seq_counter: monotonic incrementing seq
 * - data_write_ptr: next free address in data area
 * - index_write_ptr: next free address in index area
 */

#define SLOT_MAP_CAPACITY 1024U // tunable: max number of distinct slots cached

typedef struct {
    uint32_t slot;  // 0 means empty (we avoid slot==0 by offsetting if needed)
    uint32_t addr;  // flash addr
    uint32_t seq;   // seq of stored record
} slot_map_entry_t; //

static slot_map_entry_t slot_map[SLOT_MAP_CAPACITY];
static uint32_t global_seq_counter = 1;            // monotonic incrementing seq
static uint32_t data_write_ptr = DATA_AREA_BASE;   // next free address in data area
static uint32_t index_write_ptr = INDEX_AREA_BASE; // next free address in index area

/* --------------------------- Low-level helpers --------------------------- */
static inline uint32_t align_up(uint32_t v, uint32_t a) {
    return (v + a - 1u) / a * a;
}

/**
 * @description: 初始化slot_map哈希表，将所有条目清零（标记为空）
 */
static void slot_map_init(void) {
    memset(slot_map, 0, sizeof(slot_map));
}

// Knuth乘法哈希: 2654435761 = 2^32 * (√5-1)/2 (黄金分割点)
#define HASH_MARK (SLOT_MAP_CAPACITY - 1)
#define SLOT_MAP_HASH(x) ((x * 2654435761u) & HASH_MARK)

/**
 * @description:
 * 查找指定slot在哈希表中的索引位置，使用线性探测法解决哈希冲突，遇到空槽位表示查找失败，放入的时候就算有数据也是往后顺延，所以得到0的话，肯定是不存在这个slot
 * @param {uint32_t} slot: 要查找的槽位号
 * @return {int} 成功返回索引位置，失败返回-1（未找到或表空）
 *
 * 实现说明:
 * 1. 使用Knuth乘法哈希算法计算初始索引位置
 * 2. 采用线性探测法解决哈希冲突
 * 3. 遇到空槽位(slot==0)表示查找失败，放入的时候就算有数据也是往后顺延，所以得到0的话，肯定是不存在这个slot
 */
static int slot_map_find_hash(uint32_t slot) {
    uint32_t idx = SLOT_MAP_HASH(slot);

    // 线性探测: 从计算位置开始顺序查找
    for (uint32_t i = 0; i < SLOT_MAP_CAPACITY; ++i) {
        uint32_t p = (idx + i) % SLOT_MAP_CAPACITY;

        // 遇到空槽位，说明slot不存在，放入的时候就算有数据也是往后顺延，所以得到0的话，肯定是不存在这个slot
        if (slot_map[p].slot == 0)
            return -1;

        // 找到目标slot
        if (slot_map[p].slot == slot)
            return (int)p;
    }
    return -1; // 整个表搜索完毕未找到
}

/**
 * @description: 插入slot映射条目
 * @param {uint32_t} slot: 槽位号
 * @param {uint32_t} addr: 对应的闪存物理地址
 * @param {uint32_t} seq: 序列号
 * @return {int} 成功返回存储位置索引，失败返回-1（哈希表已满）
 *
 * 实现说明:
 * 1. 如果slot不存在则寻找空槽位插入
 * 2. 使用与查找相同的哈希算法和冲突解决策略
 */
static int slot_map_put(uint32_t slot, uint32_t addr, uint32_t seq) {
    uint32_t idx = SLOT_MAP_HASH(slot);
    for (uint32_t i = 0; i < SLOT_MAP_CAPACITY; ++i) {
        uint32_t p = (idx + i) % SLOT_MAP_CAPACITY;

        // 找到空槽位，进行插入
        if (slot_map[p].slot == 0) {
            slot_map[p].slot = slot;
            slot_map[p].addr = addr;
            slot_map[p].seq = seq;
            return (int)p;
        }
    }
    return -1; // 哈希表已满，无法插入
}

/**
 * @description: 获取指定slot的地址和序列号
 * @param {uint32_t} slot: 要查询的槽位号
 * @param {uint32_t*} addr_out: 输出参数-闪存地址
 * @param {uint32_t*} seq_out: 输出参数-序列号
 * @return {int} 成功返回0，失败返回-1（未找到）
 */
static int slot_map_get(uint32_t slot, uint32_t* addr_out, uint32_t* seq_out) {
    // 先查找slot在哈希表中的位置
    int idx = slot_map_find_hash(slot);
    if (idx < 0)
        return -1; // 未找到

    // 通过输出参数返回结果
    if (addr_out)
        *addr_out = slot_map[idx].addr;
    if (seq_out)
        *seq_out = slot_map[idx].seq;

    return 0;
}

/* --------------------------- Index Area ops ------------------------------ */
// Append index entry (index_write_ptr moves forward). Index area is append-only.
static int index_append(uint32_t slot, uint32_t addr, uint32_t seq) {
    if (index_write_ptr + INDEX_ENTRY_SIZE > INDEX_AREA_BASE + INDEX_AREA_SIZE) {
        FS_LOG("Index area full!");
        return -1;
    }
    index_entry_t ie;
    ie.slot = slot;
    ie.addr = addr;
    ie.seq = seq;
    ie.crc16 = 0;
    ie._pad = 0;
    uint8_t buf[INDEX_ENTRY_SIZE];
    memcpy(buf, &ie, sizeof(ie));
    uint16_t crc = crc16_ccitt(buf, sizeof(ie) - 4, 0xFFFF); // exclude crc+pad
    // place crc into buffer
    buf[12] = (uint8_t)(crc & 0xFF);
    buf[13] = (uint8_t)(crc >> 8);
    // buf[14..15] reserved (pad)

    int rc = HAL_FLASH_PROGRAM(index_write_ptr, buf, INDEX_ENTRY_SIZE);
    if (rc < 0) {
        return rc;
    }
    index_write_ptr += INDEX_ENTRY_SIZE;
    return 0;
}

/* --------------------------- Record write/read --------------------------- */
// Write a single record atomically (append-only)
static int fs_write_record(uint32_t timestamp, const void* payload) {
    uint32_t slot = timestamp / SLOT_SECONDS;
    uint32_t seq = global_seq_counter++;

    // ensure space in data area
    if (data_write_ptr + RECORD_TOTAL_SIZE > DATA_AREA_BASE + DATA_AREA_SIZE) {
        FS_LOG("Data area full -> trigger GC");
        // caller should trigger GC; return error for now
        return -2;
    }

    rec_hdr_t hdr;
    hdr.slot = slot;
    hdr.seq = seq;
    hdr.timestamp = timestamp;
    hdr.flags = FLAG_WRITING;
    hdr.crc16 = 0;

    uint8_t write_buf[RECORD_TOTAL_SIZE];
    memcpy(write_buf, &hdr, RECORD_HDR_SIZE);
    memcpy(write_buf + RECORD_HDR_SIZE, payload, RECORD_PAYLOAD_SIZE);
    uint16_t crc = crc16_ccitt(write_buf, RECORD_TOTAL_SIZE - 2, 0xFFFF); // exclude crc field
    // place crc at last two bytes of header (offset 14)
    write_buf[14] = (uint8_t)(crc & 0xFF);
    write_buf[15] = (uint8_t)(crc >> 8);

    // program record (assume HAL_FLASH_PROGRAM can program unaligned; otherwise page-align)
    int rc = HAL_FLASH_PROGRAM(data_write_ptr, write_buf, RECORD_TOTAL_SIZE);
    if (rc < 0) {
        FS_LOG("flash program failed rc=%d", rc);
        return rc;
    }

    // mark VALID flag: we can either rewrite flags field or rely on index append as commit.
    // For simplicity, we'll assume writing header with FLAG_VALID set in place of WRITING.
    // We'll flip flags in header by programming the 2 bytes containing flags (offset 12).
    uint8_t flags_valid = FLAG_VALID;
    rc = HAL_FLASH_PROGRAM(data_write_ptr + 12, &flags_valid, sizeof(flags_valid));
    if (rc < 0) {
        FS_LOG("flag set failed");
        return rc;
    }

    // append index entry (commit)
    rc = index_append(slot, data_write_ptr, seq);
    if (rc < 0) {
        FS_LOG("index append failed");
        return rc;
    }

    // update in-RAM map
    slot_map_put(slot, data_write_ptr, seq);

    data_write_ptr += RECORD_TOTAL_SIZE;
    // align to page maybe
    data_write_ptr = align_up(data_write_ptr, PAGE_SIZE);

    return 0;
}

// Read record by timestamp (fast path uses in-RAM slot map)
static int fs_read_by_timestamp(uint32_t timestamp, void* payload_out) {
    uint32_t slot = timestamp / SLOT_SECONDS;
    uint32_t addr, seq;
    if (slot_map_get(slot, &addr, &seq) == 0) {
        // read header + payload
        uint8_t buf[RECORD_TOTAL_SIZE];
        if (HAL_FLASH_READ(addr, buf, RECORD_TOTAL_SIZE) != 0)
            return -1;
        rec_hdr_t hdr;
        memcpy(&hdr, buf, RECORD_HDR_SIZE);
        // verify flags and crc
        if (!(hdr.flags & FLAG_VALID))
            return -2; // not valid
        uint16_t calc = crc16_ccitt(buf, RECORD_TOTAL_SIZE - 2, 0xFFFF);
        uint16_t stored = (uint16_t)buf[14] | ((uint16_t)buf[15] << 8);
        if (calc != stored)
            return -3; // crc fail
        memcpy(payload_out, buf + RECORD_HDR_SIZE, RECORD_PAYLOAD_SIZE);
        return 0;
    }
    return -4; // not found
}

/* --------------------------- Index rebuild on boot ----------------------- */
// 顺序扫描索引区域，重建槽映射并设置写指针
static int rebuild_index(void) {
    FS_LOG("Rebuilding index...");
    slot_map_init();
    uint32_t ptr = INDEX_AREA_BASE;
    uint32_t last_index_ptr = INDEX_AREA_BASE;
    uint8_t buf[INDEX_ENTRY_SIZE];

    while (ptr + INDEX_ENTRY_SIZE <= INDEX_AREA_BASE + INDEX_AREA_SIZE) {
        if (HAL_FLASH_READ(ptr, buf, INDEX_ENTRY_SIZE) != 0)
            break; // read error -> stop

        // 检查是否为擦除状态（只要有一个数据就不是擦除状态），如果是则停止扫描
        bool empty = true;
        for (uint32_t i = 0; i < INDEX_ENTRY_SIZE; ++i)
            if (buf[i] != 0xFF) {
                empty = false;
                break;
            }
        if (empty)
            break;

        // 计算前12个字节的CRC，检查数据有效性
        uint16_t crc = crc16_ccitt(buf, 12, 0xFFFF);
        uint16_t stored = (uint16_t)buf[12] | ((uint16_t)buf[13] << 8);
        if (crc != stored) {
            FS_LOG("Index entry crc mismatch at 0x%08X, stopping", ptr);
            break; // partial/garbled entry (likely due to power loss); stop scanning
        }

        // crc校验正确，解析索引条目
        index_entry_t ie;
        memcpy(&ie, buf, sizeof(ie));
        // 计算当前索引数据在hash表中的位置，没有则插入，有则比较seq大小，更新
        int hash = slot_map_find_hash(ie.slot);
        if (hash < 0) {
            slot_map_put(ie.slot, ie.addr, ie.seq);
        } else {
            if (ie.seq >= slot_map[hash].seq) {
                slot_map[hash].addr = ie.addr;
                slot_map[hash].seq = ie.seq;
            }
        }
        ptr += INDEX_ENTRY_SIZE;
        last_index_ptr = ptr;
    }
    index_write_ptr = last_index_ptr;

    // 通过索引直接计算data_write_ptr，避免扫描整个数据区域
    // 由于数据是顺序写入的，我们只需要找到序列号最大的记录（最后写入的记录）
    uint32_t max_seq = 0;
    uint32_t last_record_addr = 0;
    bool found_any_record = false;

    // 遍历slot_map，找到序列号最大的记录
    for (uint32_t i = 0; i < SLOT_MAP_CAPACITY; ++i) {
        if (slot_map[i].slot != 0) { // 非空槽位
            found_any_record = true;
            if (slot_map[i].seq > max_seq) {
                max_seq = slot_map[i].seq;
                last_record_addr = slot_map[i].addr;
            }
        }
    }

    if (found_any_record) {
        // 找到了最后一条记录，计算下一个写入位置
        data_write_ptr = last_record_addr + RECORD_TOTAL_SIZE;
        data_write_ptr = align_up(data_write_ptr, PAGE_SIZE);

        // 更新全局序列计数器，确保下次写入时序列号正确
        global_seq_counter = max_seq + 1;

        FS_LOG("Found last record at 0x%08X (seq=%u), next write at 0x%08X",
               last_record_addr, max_seq, data_write_ptr);
    } else {
        // 没有找到任何记录，从数据区域开始写入
        data_write_ptr = DATA_AREA_BASE;
        global_seq_counter = 1;
        FS_LOG("No records found, starting from data area base 0x%08X", data_write_ptr);
    }
    FS_LOG("Index rebuild done. index_ptr=0x%08X data_ptr=0x%08X", index_write_ptr, data_write_ptr);
    return 0;
}

/* ------------------------------- GC (simple) ----------------------------- */
/**
 * @description: 简单的垃圾回收函数
 * @return {void} 无返回值
 * 工作原理:
 * 1. 扫描数据区域中的所有扇区，计算每个扇区的存活记录比率
 * 2. 选择存活率最低的扇区进行回收（存活率 = 活跃记录数/总记录数）
 * 3. 将选中扇区中的活跃记录迁移到新位置
 * 4. 擦除已迁移完的扇区
 *
 * 注意:
 * - 活跃记录: 指在slot_map中记录的最新数据
 * - 存活率: 扇区中活跃记录的占比，比率越低说明可回收的空间越多
 */
static void do_gc_once(void) {
    FS_LOG("GC: scanning sectors...");
    uint32_t best_sector = 0xFFFFFFFFU; // 存储待回收的最佳扇区地址
    float best_score = 1.0f;            // 存储最低的存活率（初始值为1.0）
    uint32_t data_end = DATA_AREA_BASE + DATA_AREA_SIZE;
    // 遍历数据区域的所有扇区
    for (uint32_t sec = DATA_AREA_BASE; sec + SECTOR_SIZE <= data_end; sec += SECTOR_SIZE) {
        uint32_t live = 0;   // 扇区中活跃记录的数量
        uint32_t total = 0;  // 扇区中总记录数量
        uint32_t addr = sec; // 当前扫描地址

        // 扫描当前扇区中的所有记录
        for (; addr + RECORD_TOTAL_SIZE <= sec + SECTOR_SIZE; addr += RECORD_TOTAL_SIZE) {
            uint8_t hdr[16]; // 用于读取记录头部
            if (HAL_FLASH_READ(addr, hdr, 16) != 0)
                break;

            // 检查记录是否已被擦除（全0xFF）
            bool erased = true;
            for (int i = 0; i < 16; i++)
                if (hdr[i] != 0xFF) {
                    erased = false;
                    break;
                }
            if (erased)
                break;

            // 检查slot是否为擦除状态（0xFFFFFFFF），如果是则跳过
            uint32_t slot = *((uint32_t*)&hdr[0]);
            if (slot == 0xFFFFFFFF)
                continue;

            total++; // 找到一条有效记录

            // 检查该记录是否为slot_map中的最新记录
            int idx = slot_map_find_hash(slot);
            if (idx >= 0 && slot_map[idx].addr == addr)
                live++; // 找到一条活跃记录
        }
        // 如果扇区为空，跳过该扇区
        if (total == 0)
            continue;

        // 计算扇区的存活率 = 活跃记录数/总记录数
        float live_ratio = (float)live / (float)total;
        // 如果找到存活率更低的扇区，更新最佳候选扇区
        if (live_ratio < best_score) {
            best_score = live_ratio;
            best_sector = sec;
        }
    }

    // 如果没有找到合适的扇区（所有扇区都是空的或都是活跃的）
    if (best_sector == 0xFFFFFFFFU) {
        FS_LOG("GC: no candidate");
        return;
    }
    FS_LOG("GC: picking sector 0x%08X (live_ratio=%.2f)", best_sector, best_score);
    // 开始迁移最佳候选扇区中的活跃记录
    uint32_t addr = best_sector;
    for (; addr + RECORD_TOTAL_SIZE <= best_sector + SECTOR_SIZE; addr += RECORD_TOTAL_SIZE) {
        // 读取记录头部
        uint8_t hdr[16];
        if (HAL_FLASH_READ(addr, hdr, 16) != 0)
            break;

        // 检查记录是否已被擦除
        bool erased = true;
        for (int i = 0; i < 16; i++)
            if (hdr[i] != 0xFF) {
                erased = false;
                break;
            }
        if (erased)
            break;

        // 获取记录的slot值
        uint32_t slot = *((uint32_t*)&hdr[0]);
        // 检查是否为活跃记录
        int idx = slot_map_find_hash(slot);
        if (idx >= 0 && slot_map[idx].addr == addr) {
            // 是活跃记录，需要迁移
            uint8_t recbuf[RECORD_TOTAL_SIZE];
            if (HAL_FLASH_READ(addr, recbuf, RECORD_TOTAL_SIZE) != 0)
                break;

            // 验证记录的CRC
            uint16_t calc = crc16_ccitt(recbuf, RECORD_TOTAL_SIZE - 2, 0xFFFF);
            uint16_t stored = (uint16_t)recbuf[14] | ((uint16_t)recbuf[15] << 8);
            if (calc != stored) {
                FS_LOG("GC: skipping corrupted record at 0x%08X", addr);
                continue;
            }

            // 将记录写入新位置
            uint32_t new_addr = data_write_ptr;
            if (new_addr + RECORD_TOTAL_SIZE > DATA_AREA_BASE + DATA_AREA_SIZE) {
                FS_LOG("GC: no space to migrate");
                return;
            }
            if (HAL_FLASH_PROGRAM(new_addr, recbuf, RECORD_TOTAL_SIZE) != 0) {
                FS_LOG("GC: migrate program failed");
                return;
            }

            // 更新索引和内存映射
            index_append(slot, new_addr, *((uint32_t*)&recbuf[4]));
            slot_map_put(slot, new_addr, *((uint32_t*)&recbuf[4]));
            // 更新写指针，并对齐到页边界
            data_write_ptr = align_up(new_addr + RECORD_TOTAL_SIZE, PAGE_SIZE);
        }
    }

    // 所有活跃记录迁移完成后，擦除整个扇区
    if (HAL_FLASH_ERASE_SECTOR(best_sector) != 0) {
        FS_LOG("GC: erase failed");
        return;
    }
    FS_LOG("GC: erased sector 0x%08X", best_sector);
}

/* --------------------------- Public API / init --------------------------- */
int fs_init(void) {
    // rebuild index & set pointers
    if (rebuild_index() != 0)
        return -1;
    // load global_seq_counter from meta area if desired
    return 0;
}

// user-facing write
int fs_store_timestamped(uint32_t timestamp, const void* payload) {
    int rc = fs_write_record(timestamp, payload);
    if (rc == -2) {
        // no space -> try GC and retry once
        do_gc_once();
        rc = fs_write_record(timestamp, payload);
    }
    return rc;
}

// user-facing read
int fs_load_by_timestamp(uint32_t timestamp, void* payload_out) {
    return fs_read_by_timestamp(timestamp, payload_out);
}

// user-facing GC trigger
void fs_gc(void) {
    do_gc_once();
}