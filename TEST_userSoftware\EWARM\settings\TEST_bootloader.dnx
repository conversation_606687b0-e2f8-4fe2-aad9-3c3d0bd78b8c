<?xml version="1.0"?>
<settings>
    <Stack>
        <FillEnabled>0</FillEnabled>
        <OverflowWarningsEnabled>1</OverflowWarningsEnabled>
        <WarningThreshold>90</WarningThreshold>
        <SpWarningsEnabled>1</SpWarningsEnabled>
        <WarnLogOnly>1</WarnLogOnly>
        <UseTrigger>1</UseTrigger>
        <TriggerName>main</TriggerName>
        <LimitSize>0</LimitSize>
        <ByteLimit>50</ByteLimit>
    </Stack>
    <StLinkDriver>
        <stlinkserialNo>48FF66064885555233511187</stlinkserialNo>
        <stlinkfoundProbes />
        <LeaveTargetRunning>_ 0</LeaveTargetRunning>
        <CStepIntDis>_ 0</CStepIntDis>
        <stlinkResetStyle>0</stlinkResetStyle>
        <stlinkResetStrategy>2</stlinkResetStrategy>
    </StLinkDriver>
    <Exceptions>
        <StopOnUncaught>_ 0</StopOnUncaught>
        <StopOnThrow>_ 0</StopOnThrow>
    </Exceptions>
    <CallStack>
        <ShowArgs>0</ShowArgs>
    </CallStack>
    <SWOTraceHWSettings>
        <OverrideDefaultClocks>0</OverrideDefaultClocks>
        <CpuClock>64000000</CpuClock>
        <ClockAutoDetect>0</ClockAutoDetect>
        <ClockWanted>2000000</ClockWanted>
        <JtagSpeed>2000000</JtagSpeed>
        <Prescaler>32</Prescaler>
        <TimeStampPrescIndex>0</TimeStampPrescIndex>
        <TimeStampPrescData>0</TimeStampPrescData>
        <PcSampCYCTAP>1</PcSampCYCTAP>
        <PcSampPOSTCNT>15</PcSampPOSTCNT>
        <PcSampIndex>0</PcSampIndex>
        <DataLogMode>0</DataLogMode>
        <ITMportsEnable>0</ITMportsEnable>
        <ITMportsTermIO>0</ITMportsTermIO>
        <ITMportsLogFile>0</ITMportsLogFile>
        <ITMlogFile>$PROJ_DIR$\ITM.log</ITMlogFile>
    </SWOTraceHWSettings>
    <DebugChecksum>
        <Checksum>28049518</Checksum>
    </DebugChecksum>
    <CodeCoverage>
        <Enabled>_ 0</Enabled>
    </CodeCoverage>
    <Disassembly>
        <MixedMode>1</MixedMode>
        <InstrCount>0</InstrCount>
    </Disassembly>
    <LogFile>
        <LoggingEnabled>_ 0</LoggingEnabled>
        <LogFile>_ ""</LogFile>
        <Category>_ 0</Category>
    </LogFile>
    <CallStackLog>
        <Enabled>0</Enabled>
    </CallStackLog>
    <CallStackStripe>
        <ShowTiming>1</ShowTiming>
    </CallStackStripe>
    <PlDriver>
        <FirstRun>0</FirstRun>
        <MemConfigValue>D:\software\iar\arm\config\debugger\ST\STM32F103ZE.ddf</MemConfigValue>
    </PlDriver>
    <ArmDriver>
        <EnableCache>0</EnableCache>
        <EnforceMemoryConfiguration>1</EnforceMemoryConfiguration>
    </ArmDriver>
    <Trace1>
        <Enabled>0</Enabled>
        <ShowSource>1</ShowSource>
    </Trace1>
    <TermIOLog>
        <LoggingEnabled>_ 0</LoggingEnabled>
        <LogFile>_ ""</LogFile>
    </TermIOLog>
    <DisassembleMode>
        <mode>0</mode>
    </DisassembleMode>
    <array_types>
        <Fmt0>uint8_t[6]	3	0</Fmt0>
    </array_types>
    <Trace2>
        <Enabled>0</Enabled>
        <ShowSource>0</ShowSource>
    </Trace2>
    <SWOTraceWindow>
        <PcSampling>0</PcSampling>
        <InterruptLogs>0</InterruptLogs>
        <ForcedTimeStamps>0</ForcedTimeStamps>
        <EventCPI>0</EventCPI>
        <EventEXC>0</EventEXC>
        <EventFOLD>0</EventFOLD>
        <EventLSU>0</EventLSU>
        <EventSLEEP>0</EventSLEEP>
    </SWOTraceWindow>
    <DataLog>
        <GraphEnabled>0</GraphEnabled>
        <LogEnabled>0</LogEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <SumEnabled>0</SumEnabled>
        <ShowTimeSum>1</ShowTimeSum>
    </DataLog>
    <InterruptLog>
        <GraphEnabled>0</GraphEnabled>
        <LogEnabled>0</LogEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <SumEnabled>0</SumEnabled>
        <ShowTimeSum>1</ShowTimeSum>
        <SumSortOrder>0</SumSortOrder>
    </InterruptLog>
    <EventLog>
        <GraphEnabled>0</GraphEnabled>
        <LogEnabled>0</LogEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <Title_0>Ch3</Title_0>
        <Symbol_0>0 0 1</Symbol_0>
        <Title_1>Ch2</Title_1>
        <Symbol_1>0 0 1</Symbol_1>
        <Title_2>Ch1</Title_2>
        <Symbol_2>0 0 1</Symbol_2>
        <Title_3>Ch0</Title_3>
        <Symbol_3>0 0 1</Symbol_3>
        <SumEnabled>0</SumEnabled>
        <ShowTimeSum>1</ShowTimeSum>
        <SumSortOrder>0</SumSortOrder>
    </EventLog>
    <Aliases>
        <Count>0</Count>
        <SuppressDialog>0</SuppressDialog>
    </Aliases>
    <Breakpoints2>
        <Count>0</Count>
    </Breakpoints2>
    <DriverProfiling>
        <Enabled>0</Enabled>
        <Mode>3</Mode>
        <Graph>0</Graph>
        <Symbiont>0</Symbiont>
        <Exclusions />
    </DriverProfiling>
</settings>
