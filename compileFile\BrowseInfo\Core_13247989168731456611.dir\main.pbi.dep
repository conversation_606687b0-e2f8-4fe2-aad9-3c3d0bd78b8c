D:\routine\stm32f1_software\software_All_example\compileFile\BrowseInfo\Core_13247989168731456611.dir\main.pbi: \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\mid\uplinkUartMid.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\mid\backUartMid.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\mid\debugUartMid.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\mid\mid_head.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\app\W25Q128_flash.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\app\led.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\app\light.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\app\lsd.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\app\structAndFuc.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\app\dataSerialization.h \
  D:\software\iar\arm\inc\c\stdarg.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\app\sfud\sfud_flash_def.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\app\sfud\sfud_cfg.h \
  D:\software\iar\arm\inc\c\DLib_Product_stdlib.h \
  D:\software\iar\arm\inc\c\ycheck.h D:\software\iar\arm\inc\c\stdlib.h \
  D:\software\iar\arm\inc\c\stdio.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\app\sfud\sfud_def.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\app\sfud\sfud.h \
  D:\software\iar\arm\inc\c\DLib_Product_string.h \
  D:\software\iar\arm\inc\c\string.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\app\print.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\app\animal.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\app\debugApp.h \
  D:\software\iar\arm\inc\c\stdbool.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\app\ringbuffer.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\app\userRtos.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\include\queue.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\include\list.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\include\task.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\app\app_head.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\Core\Inc\usart.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\Core\Inc\spi.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\Core\Inc\gpio.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\Core\Inc\dma.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\include\mpu_wrappers.h \
  D:\software\iar\arm\inc\c\aarch32\iar_intrinsics_common.h \
  D:\software\iar\arm\inc\c\aarch32\intrinsics.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\IAR\ARM_CM3\portmacro.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\include\deprecated_definitions.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\include\portable.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\include\projdefs.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\Core\Inc\FreeRTOSConfig.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\include\FreeRTOS.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h \
  D:\software\iar\arm\inc\c\ysizet.h D:\software\iar\arm\inc\c\stddef.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h \
  D:\software\iar\arm\inc\c\aarch32\iccarm_builtin.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\CMSIS\Include\cmsis_iccarm.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\CMSIS\Include\cmsis_compiler.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\CMSIS\Include\cmsis_version.h \
  D:\software\iar\arm\inc\c\DLib_Product.h \
  D:\software\iar\arm\inc\c\DLib_Config_Full.h \
  D:\software\iar\arm\inc\c\DLib_Defaults.h \
  D:\software\iar\arm\inc\c\yvals.h D:\software\iar\arm\inc\c\stdint.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\CMSIS\Include\core_cm3.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h \
  C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\Core\Inc\stm32f1xx_hal_conf.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM\..\Core\Inc\main.h \
  D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\Core\Src\main.c
