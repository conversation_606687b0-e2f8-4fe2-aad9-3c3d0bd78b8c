#MicroXplorer Configuration settings - do not modify
Dma.Request0=USART1_RX
Dma.Request1=USART3_RX
Dma.Request2=USART2_RX
Dma.RequestsNb=3
Dma.USART1_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.0.Instance=DMA1_Channel5
Dma.USART1_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.0.Mode=DMA_CIRCULAR
Dma.USART1_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.0.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
Dma.USART2_RX.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART2_RX.2.Instance=DMA1_Channel6
Dma.USART2_RX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_RX.2.MemInc=DMA_MINC_ENABLE
Dma.USART2_RX.2.Mode=DMA_CIRCULAR
Dma.USART2_RX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_RX.2.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_RX.2.Priority=DMA_PRIORITY_LOW
Dma.USART2_RX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
Dma.USART3_RX.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART3_RX.1.Instance=DMA1_Channel3
Dma.USART3_RX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_RX.1.MemInc=DMA_MINC_ENABLE
Dma.USART3_RX.1.Mode=DMA_CIRCULAR
Dma.USART3_RX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_RX.1.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_RX.1.Priority=DMA_PRIORITY_LOW
Dma.USART3_RX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
FREERTOS.FootprintOK=true
FREERTOS.IPParameters=Tasks01,configTOTAL_HEAP_SIZE,FootprintOK,configMAX_TASK_NAME_LEN
FREERTOS.Tasks01=defaultTask,24,4096,StartDefaultTask,Default,1,Dynamic,NULL,NULL
FREERTOS.configMAX_TASK_NAME_LEN=32
FREERTOS.configTOTAL_HEAP_SIZE=30720
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F103ZET6
Mcu.Family=STM32F1
Mcu.IP0=DMA
Mcu.IP1=FREERTOS
Mcu.IP2=I2C1
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=SPI2
Mcu.IP6=SYS
Mcu.IP7=USART1
Mcu.IP8=USART2
Mcu.IP9=USART3
Mcu.IPNb=10
Mcu.Name=STM32F103Z(C-D-E)Tx
Mcu.Package=LQFP144
Mcu.Pin0=PA2
Mcu.Pin1=PA3
Mcu.Pin10=PA13
Mcu.Pin11=PA14
Mcu.Pin12=PB6
Mcu.Pin13=PB7
Mcu.Pin14=VP_FREERTOS_VS_CMSIS_V2
Mcu.Pin15=VP_SYS_VS_tim1
Mcu.Pin2=PB12
Mcu.Pin3=PB13
Mcu.Pin4=PB14
Mcu.Pin5=PB15
Mcu.Pin6=PD8
Mcu.Pin7=PD9
Mcu.Pin8=PA9
Mcu.Pin9=PA10
Mcu.PinsNb=16
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F103ZETx
MxCube.Version=6.5.0
MxDb.Version=DB.6.0.50
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:true
NVIC.DMA1_Channel3_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Channel5_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Channel6_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:true
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:true
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:true
NVIC.PendSV_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:false\:true
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:false\:false\:false\:false\:true
NVIC.SavedPendsvIrqHandlerGenerated=true
NVIC.SavedSvcallIrqHandlerGenerated=true
NVIC.SavedSystickIrqHandlerGenerated=true
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:true\:true
NVIC.TIM1_UP_IRQn=true\:15\:0\:false\:false\:true\:false\:false\:true\:true
NVIC.TimeBase=TIM1_UP_IRQn
NVIC.TimeBaseIP=TIM1
NVIC.USART1_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:true
PA10.GPIOParameters=GPIO_Label
PA10.GPIO_Label=deBugRx
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.GPIOParameters=GPIO_Label
PA2.GPIO_Label=IAPtx
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.GPIOParameters=GPIO_Label
PA3.GPIO_Label=IAPrx
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA9.GPIOParameters=GPIO_Label
PA9.GPIO_Label=deBugTx
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB12.GPIOParameters=GPIO_Label
PB12.GPIO_Label=W25_CS
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.GPIOParameters=GPIO_Label
PB13.GPIO_Label=W25_SCK
PB13.Mode=Full_Duplex_Master
PB13.Signal=SPI2_SCK
PB14.GPIOParameters=GPIO_Label
PB14.GPIO_Label=W25_MI
PB14.Mode=Full_Duplex_Master
PB14.Signal=SPI2_MISO
PB15.GPIOParameters=GPIO_Label
PB15.GPIO_Label=W25_MO
PB15.Mode=Full_Duplex_Master
PB15.Signal=SPI2_MOSI
PB6.GPIOParameters=GPIO_Label
PB6.GPIO_Label=_24c02_SCL
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB7.GPIOParameters=GPIO_Label
PB7.GPIO_Label=_24c02_SDA
PB7.Mode=I2C
PB7.Signal=I2C1_SDA
PD8.GPIOParameters=GPIO_Label
PD8.GPIO_Label=upLinkTx
PD8.Mode=Asynchronous
PD8.Signal=USART3_TX
PD9.GPIOParameters=GPIO_Label
PD9.GPIO_Label=upLinkRx
PD9.Mode=Asynchronous
PD9.Signal=USART3_RX
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103ZETx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.8.4
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=false
ProjectManager.LibraryCopy=2
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=TEST_bootloader.ioc
ProjectManager.ProjectName=TEST_bootloader
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x1000
ProjectManager.TargetToolchain=EWARM V8.32
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART1_UART_Init-USART1-false-HAL-true,5-MX_USART3_UART_Init-USART3-false-HAL-true,6-MX_USART2_UART_Init-USART2-false-HAL-true,7-MX_SPI2_Init-SPI2-false-HAL-true,8-MX_I2C1_Init-I2C1-false-HAL-true
RCC.ADCFreqValue=32000000
RCC.AHBFreq_Value=64000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=32000000
RCC.APB1TimFreq_Value=64000000
RCC.APB2Freq_Value=64000000
RCC.APB2TimFreq_Value=64000000
RCC.FCLKCortexFreq_Value=64000000
RCC.FSMCFreq_Value=64000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=64000000
RCC.I2S2Freq_Value=64000000
RCC.I2S3Freq_Value=64000000
RCC.IPParameters=ADCFreqValue,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,FCLKCortexFreq_Value,FSMCFreq_Value,FamilyName,HCLKFreq_Value,I2S2Freq_Value,I2S3Freq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLMUL,SDIOFreq_Value,SDIOHCLKDiv2FreqValue,SYSCLKFreq_VALUE,SYSCLKSource,TimSysFreq_Value,USBFreq_Value,VCOOutput2Freq_Value
RCC.MCOFreq_Value=64000000
RCC.PLLCLKFreq_Value=64000000
RCC.PLLMCOFreq_Value=32000000
RCC.PLLMUL=RCC_PLL_MUL16
RCC.SDIOFreq_Value=64000000
RCC.SDIOHCLKDiv2FreqValue=32000000
RCC.SYSCLKFreq_VALUE=64000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TimSysFreq_Value=64000000
RCC.USBFreq_Value=64000000
RCC.VCOOutput2Freq_Value=4000000
SPI2.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_2
SPI2.CalculateBaudRate=16.0 MBits/s
SPI2.Direction=SPI_DIRECTION_2LINES
SPI2.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,BaudRatePrescaler
SPI2.Mode=SPI_MODE_MASTER
SPI2.VirtualType=VM_MASTER
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
VP_FREERTOS_VS_CMSIS_V2.Mode=CMSIS_V2
VP_FREERTOS_VS_CMSIS_V2.Signal=FREERTOS_VS_CMSIS_V2
VP_SYS_VS_tim1.Mode=TIM1
VP_SYS_VS_tim1.Signal=SYS_VS_tim1
board=custom
