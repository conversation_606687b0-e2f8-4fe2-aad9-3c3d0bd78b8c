[PreviousLibFiles]
LibFiles=Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h;Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h;Middlewares\Third_Party\FreeRTOS\Source\include\croutine.h;Middlewares\Third_Party\FreeRTOS\Source\include\deprecated_definitions.h;Middlewares\Third_Party\FreeRTOS\Source\include\event_groups.h;Middlewares\Third_Party\FreeRTOS\Source\include\FreeRTOS.h;Middlewares\Third_Party\FreeRTOS\Source\include\list.h;Middlewares\Third_Party\FreeRTOS\Source\include\message_buffer.h;Middlewares\Third_Party\FreeRTOS\Source\include\mpu_prototypes.h;Middlewares\Third_Party\FreeRTOS\Source\include\mpu_wrappers.h;Middlewares\Third_Party\FreeRTOS\Source\include\portable.h;Middlewares\Third_Party\FreeRTOS\Source\include\projdefs.h;Middlewares\Third_Party\FreeRTOS\Source\include\queue.h;Middlewares\Third_Party\FreeRTOS\Source\include\semphr.h;Middlewares\Third_Party\FreeRTOS\Source\include\stack_macros.h;Middlewares\Third_Party\FreeRTOS\Source\include\StackMacros.h;Middlewares\Third_Party\FreeRTOS\Source\include\stream_buffer.h;Middlewares\Third_Party\FreeRTOS\Source\include\task.h;Middlewares\Third_Party\FreeRTOS\Source\include\timers.h;Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.h;Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os.h;Middlewares\Third_Party\FreeRTOS\Source\portable\IAR\ARM_CM3\portmacro.h;Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c;Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c;Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c;Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c;Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c;Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c;Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c;Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c;Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c;Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c;Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_i2c.c;Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_spi.c;Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c;Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c;Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c;Middlewares\Third_Party\FreeRTOS\Source\croutine.c;Middlewares\Third_Party\FreeRTOS\Source\event_groups.c;Middlewares\Third_Party\FreeRTOS\Source\list.c;Middlewares\Third_Party\FreeRTOS\Source\queue.c;Middlewares\Third_Party\FreeRTOS\Source\stream_buffer.c;Middlewares\Third_Party\FreeRTOS\Source\tasks.c;Middlewares\Third_Party\FreeRTOS\Source\timers.c;Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c;Middlewares\Third_Party\FreeRTOS\Source\portable\MemMang\heap_4.c;Middlewares\Third_Party\FreeRTOS\Source\portable\IAR\ARM_CM3\port.c;Middlewares\Third_Party\FreeRTOS\Source\portable\IAR\ARM_CM3\portasm.s;Drivers\STM32F1xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_def.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_rcc_ex.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_gpio_ex.h;Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma_ex.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_dma.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_cortex.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_pwr.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_flash_ex.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_exti.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_i2c.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_spi.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_tim_ex.h;Drivers\STM32F1xx_HAL_Driver\Inc\stm32f1xx_hal_uart.h;Middlewares\Third_Party\FreeRTOS\Source\include\croutine.h;Middlewares\Third_Party\FreeRTOS\Source\include\deprecated_definitions.h;Middlewares\Third_Party\FreeRTOS\Source\include\event_groups.h;Middlewares\Third_Party\FreeRTOS\Source\include\FreeRTOS.h;Middlewares\Third_Party\FreeRTOS\Source\include\list.h;Middlewares\Third_Party\FreeRTOS\Source\include\message_buffer.h;Middlewares\Third_Party\FreeRTOS\Source\include\mpu_prototypes.h;Middlewares\Third_Party\FreeRTOS\Source\include\mpu_wrappers.h;Middlewares\Third_Party\FreeRTOS\Source\include\portable.h;Middlewares\Third_Party\FreeRTOS\Source\include\projdefs.h;Middlewares\Third_Party\FreeRTOS\Source\include\queue.h;Middlewares\Third_Party\FreeRTOS\Source\include\semphr.h;Middlewares\Third_Party\FreeRTOS\Source\include\stack_macros.h;Middlewares\Third_Party\FreeRTOS\Source\include\StackMacros.h;Middlewares\Third_Party\FreeRTOS\Source\include\stream_buffer.h;Middlewares\Third_Party\FreeRTOS\Source\include\task.h;Middlewares\Third_Party\FreeRTOS\Source\include\timers.h;Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.h;Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os.h;Middlewares\Third_Party\FreeRTOS\Source\portable\IAR\ARM_CM3\portmacro.h;Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f103xe.h;Drivers\CMSIS\Device\ST\STM32F1xx\Include\stm32f1xx.h;Drivers\CMSIS\Device\ST\STM32F1xx\Include\system_stm32f1xx.h;Drivers\CMSIS\Device\ST\STM32F1xx\Source\Templates\system_stm32f1xx.c;Drivers\CMSIS\Include\cmsis_armcc.h;Drivers\CMSIS\Include\cmsis_armclang.h;Drivers\CMSIS\Include\cmsis_compiler.h;Drivers\CMSIS\Include\cmsis_gcc.h;Drivers\CMSIS\Include\cmsis_iccarm.h;Drivers\CMSIS\Include\cmsis_version.h;Drivers\CMSIS\Include\core_armv8mbl.h;Drivers\CMSIS\Include\core_armv8mml.h;Drivers\CMSIS\Include\core_cm0.h;Drivers\CMSIS\Include\core_cm0plus.h;Drivers\CMSIS\Include\core_cm1.h;Drivers\CMSIS\Include\core_cm23.h;Drivers\CMSIS\Include\core_cm3.h;Drivers\CMSIS\Include\core_cm33.h;Drivers\CMSIS\Include\core_cm4.h;Drivers\CMSIS\Include\core_cm7.h;Drivers\CMSIS\Include\core_sc000.h;Drivers\CMSIS\Include\core_sc300.h;Drivers\CMSIS\Include\mpu_armv7.h;Drivers\CMSIS\Include\mpu_armv8.h;Drivers\CMSIS\Include\tz_context.h;

[PreviousUsedIarFiles]
SourceFiles=..\Core\Src\main.c;..\Core\Src\gpio.c;..\Core\Src\freertos.c;..\Core\Src\dma.c;..\Core\Src\i2c.c;..\Core\Src\spi.c;..\Core\Src\usart.c;..\Core\Src\stm32f1xx_it.c;..\Core\Src\stm32f1xx_hal_msp.c;..\Core\Src\stm32f1xx_hal_timebase_tim.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_i2c.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_spi.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\croutine.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\event_groups.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\list.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\queue.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\stream_buffer.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\tasks.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\timers.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\MemMang\heap_4.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\IAR\ARM_CM3\port.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\IAR\ARM_CM3\portasm.s;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\CMSIS\Device\ST\STM32F1xx\Source\Templates\system_stm32f1xx.c;..\Core\Src\system_stm32f1xx.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_i2c.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_spi.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\croutine.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\event_groups.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\list.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\queue.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\stream_buffer.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\tasks.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\timers.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\MemMang\heap_4.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\IAR\ARM_CM3\port.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\IAR\ARM_CM3\portasm.s;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\CMSIS\Device\ST\STM32F1xx\Source\Templates\system_stm32f1xx.c;..\Core\Src\system_stm32f1xx.c;;;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\croutine.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\event_groups.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\list.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\queue.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\stream_buffer.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\tasks.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\timers.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\MemMang\heap_4.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\IAR\ARM_CM3\port.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\IAR\ARM_CM3\portasm.s;
HeaderPath=C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\include;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\IAR\ARM_CM3;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\CMSIS\Device\ST\STM32F1xx\Include;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\CMSIS\Include;..\Core\Inc;
CDefines=USE_HAL_DRIVER;STM32F103xE;USE_HAL_DRIVER;USE_HAL_DRIVER;

[PreviousUsedKeilFiles]
SourceFiles=..\Core\Src\main.c;..\Core\Src\gpio.c;..\Core\Src\freertos.c;..\Core\Src\dma.c;..\Core\Src\iwdg.c;..\Core\Src\spi.c;..\Core\Src\usart.c;..\Core\Src\stm32f1xx_it.c;..\Core\Src\stm32f1xx_hal_msp.c;..\Core\Src\stm32f1xx_hal_timebase_tim.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_iwdg.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_spi.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\croutine.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\event_groups.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\list.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\queue.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\stream_buffer.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\tasks.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\timers.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\MemMang\heap_4.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\RVDS\ARM_CM3\port.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\CMSIS\Device\ST\STM32F1xx\Source\Templates\system_stm32f1xx.c;..\Core\Src\system_stm32f1xx.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_iwdg.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_spi.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\croutine.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\event_groups.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\list.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\queue.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\stream_buffer.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\tasks.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\timers.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\MemMang\heap_4.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\RVDS\ARM_CM3\port.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\CMSIS\Device\ST\STM32F1xx\Source\Templates\system_stm32f1xx.c;..\Core\Src\system_stm32f1xx.c;;;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\croutine.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\event_groups.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\list.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\queue.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\stream_buffer.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\tasks.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\timers.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\MemMang\heap_4.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\RVDS\ARM_CM3\port.c;
HeaderPath=C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc\Legacy;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\STM32F1xx_HAL_Driver\Inc;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\include;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Middlewares\Third_Party\FreeRTOS\Source\portable\RVDS\ARM_CM3;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\CMSIS\Device\ST\STM32F1xx\Include;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F1_V1.8.4\Drivers\CMSIS\Include;..\Core\Inc;
CDefines=USE_HAL_DRIVER;STM32F103xE;USE_HAL_DRIVER;USE_HAL_DRIVER;

[PreviousGenFiles]
AdvancedFolderStructure=true
HeaderFileListSize=9
HeaderFiles#0=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Inc/gpio.h
HeaderFiles#1=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Inc/FreeRTOSConfig.h
HeaderFiles#2=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Inc/dma.h
HeaderFiles#3=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Inc/i2c.h
HeaderFiles#4=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Inc/spi.h
HeaderFiles#5=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Inc/usart.h
HeaderFiles#6=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Inc/stm32f1xx_it.h
HeaderFiles#7=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Inc/stm32f1xx_hal_conf.h
HeaderFiles#8=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Inc/main.h
HeaderFolderListSize=1
HeaderPath#0=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Inc
HeaderFiles=;
SourceFileListSize=10
SourceFiles#0=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Src/gpio.c
SourceFiles#1=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Src/freertos.c
SourceFiles#2=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Src/dma.c
SourceFiles#3=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Src/i2c.c
SourceFiles#4=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Src/spi.c
SourceFiles#5=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Src/usart.c
SourceFiles#6=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Src/stm32f1xx_it.c
SourceFiles#7=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Src/stm32f1xx_hal_msp.c
SourceFiles#8=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Src/stm32f1xx_hal_timebase_tim.c
SourceFiles#9=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Src/main.c
SourceFolderListSize=1
SourcePath#0=D:/routine/stm32f1_software/software_All_example/TEST_userSoftware/Core/Src
SourceFiles=;

