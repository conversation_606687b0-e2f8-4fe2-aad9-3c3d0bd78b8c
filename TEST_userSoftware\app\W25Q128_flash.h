#ifndef W25Q_DRIVER_H
#define W25Q_DRIVER_H

#include "main.h"

// W25Q128 参数
#define W25Q_PAGE_SIZE       256
#define W25Q_SECTOR_SIZE     4096
#define W25Q_BLOCK_SIZE      65536
#define W25Q_TOTAL_SIZE      (16 * 1024 * 1024) // 16MB

// 硬件接口（由用户在 w25q_driver.c 里实现）
extern SPI_HandleTypeDef hspi2;
#define W25Q_SPI (&hspi2)

#define W25Q_CS_PORT W25_CS_GPIO_Port
#define W25Q_CS_PIN W25_CS_Pin

typedef struct W25Q128_flash
{
    int (*check_id)(void);
    int (*init)(void);
    int (*read)(uint32_t addr, uint8_t *buf, uint32_t len);
    int (*write)(uint32_t addr, const uint8_t *buf, uint32_t len);
    int (*erase_4k)(uint32_t addr); // 4KB erase sector
    int (*erase_block64k)(uint32_t addr);
}W25Q128_flash_t;

extern W25Q128_flash_t *W25Q_Flash;

#endif // W25Q_DRIVER_H
