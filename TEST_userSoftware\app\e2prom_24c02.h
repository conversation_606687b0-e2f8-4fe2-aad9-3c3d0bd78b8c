#ifndef __E2PROM_24C02_H
#define __E2PROM_24C02_H

#include "i2c.h"

// 设备I2C地址（7位，A0~A2 接地时）
#define E2PROM_24C02_ADDR  0x50
// 页大小
#define E2PROM_PAGE_SIZE   8
// 总容量
#define E2PROM_SIZE        256

// 初始化（传入I2C句柄）
int E2PROM_Init(I2C_HandleTypeDef *hi2c);

// 写一个字节
HAL_StatusTypeDef E2PROM_WriteByte(uint16_t addr, uint8_t data);

// 读一个字节
HAL_StatusTypeDef E2PROM_ReadByte(uint16_t addr, uint8_t *data);

// 写多个字节（自动处理页对齐）
HAL_StatusTypeDef E2PROM_Write(uint16_t addr, uint8_t *data, uint16_t len);

// 读多个字节
HAL_StatusTypeDef E2PROM_Read(uint16_t addr, uint8_t *data, uint16_t len);

#endif
