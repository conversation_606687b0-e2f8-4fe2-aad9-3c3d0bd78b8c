{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "cspy",
            "request": "launch",
            "name": "Launch: TEST_bootloader.TEST_bootloader",
            "target": "arm",
            "program": "${workspaceFolder}\\compileFile\\Exe\\TEST_bootloader.out",
            "driver": "ST-LINK",
            "stopOnSymbol": "main",
            "workbenchPath": "${command:iar-config.toolchain}",
            "projectPath": "${workspaceFolder}\\TEST_userSoftware\\EWARM\\TEST_bootloader.ewp",
            "projectConfiguration": "TEST_bootloader",
            "driverOptions": [
                "--crun=disabled",
                "--endian=little",
                "--cpu=Cortex-M3",
                "--fpu=None",
                "-p",
                "$TOOLKIT_DIR$\\config\\debugger\\ST\\STM32F103ZE.ddf",
                "--drv_verify_download",
                "--semihosting",
                "--device=STM32F103ZE",
                "--drv_interface=SWD",
                "--stlink_reset_strategy=0,2",
                "--drv_swo_clock_setup=64000000,0,2000000",
                "--drv_catch_exceptions=0x000",
                "--drv_debug_ap=0",
                "--stlink_probe=stlinkv2"
            ],
            "buildBeforeDebugging": "Disabled",
            "download": {
                "extraImages": [],
                "deviceMacros": [
                    "$TOOLKIT_DIR$\\config\\debugger\\ST\\STM32F1xx.dmac"
                ],
                "flashLoader": "$TOOLKIT_DIR$\\config\\flashloader\\ST\\FlashSTM32F10xxE.board"
            }
        }
    ]
}