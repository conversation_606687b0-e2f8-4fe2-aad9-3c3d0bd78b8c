
/******************************************************************************
 *@Description    :
 *@Autor          : Li
 *@Date           : 2024-04-29 20:38:17
 *@LastEditors    : Li
 ******************************************************************************/
#include "app_head.h"

typedef void (*printer_print_fn)(void *printer_, const char *str);

typedef struct printer
{
    printer_print_fn print;
} printer_t;

/***************************************************************************/
// prefix没有内存存放数据
typedef struct plain_printer
{
    const printer_t *interface;
    const char *prefix;
} plain_printer_t;

static void plain_printer_print(plain_printer_t *self, const char *str)
{
    USER_PRINTF("%s\t%s\r\n", self->prefix, str);
}

static const printer_t printer_interface = {
    .print = (printer_print_fn)plain_printer_print,
};

plain_printer_t *plain_printer_new(const char *prefix)
{
    plain_printer_t *self = pvPortMalloc(sizeof(plain_printer_t));
    if (self == NULL)
        return NULL;

    self->interface = &printer_interface;
    self->prefix = prefix;
    return self;
}

static void plain_printer_delete(plain_printer_t *self)
{
    vPortFree(self);
}

/***************************************************************************/

typedef struct color_printer
{
    const printer_t *interface;
    int enable_color;
    const char *color_command;
    char *colorBuf;
} color_printer_t;

static void color_printer_print(color_printer_t *self, const char *str)
{
    if (self->enable_color)
        USER_PRINTF("%s\t%s\t%s\r\n", self->color_command, self->colorBuf, str);
    else
        USER_PRINTF("%s", str);
}

static const printer_t color_printer = {
    .print = (printer_print_fn)color_printer_print,
};

static color_printer_t *color_printer_new(int enable_color, const char *color_command)
{
    color_printer_t *self = pvPortMalloc(sizeof(color_printer_t));
    if (self == NULL)
        return NULL;

    self->interface = &color_printer;
    self->enable_color = enable_color;
    self->color_command = (color_command == NULL ? "default_color" : color_command);
    self->colorBuf = pvPortMalloc(10);
    if (self->colorBuf == NULL)
    {
        vPortFree(self);
        return NULL;
    }
    return self;
}

void color_printer_delete(color_printer_t *self)
{
    vPortFree(self->colorBuf);
    vPortFree(self);
}

void color_printer_SetColor(color_printer_t *self, const char *color)
{
    strncpy(self->colorBuf, color, 10);
}

/*
 * 测试打印函数
 */
void test_print(void)
{
    plain_printer_t *p1 = plain_printer_new("xxxxxxxx"); // 新建简单打印机实例
    plain_printer_t *p2 = plain_printer_new("aaaa");     // 新建简单打印机实例

    color_printer_t *c1 = color_printer_new(1, NULL);     // 新建彩色打印机实例
    color_printer_t *c2 = color_printer_new(1, "ccc222"); // 新建彩色打印机实例

    color_printer_SetColor(c1, "COLOR");
    color_printer_SetColor(c2, "color");

    printer_t **p; // 打印机接口指针的指针

    p = (printer_t **)p1;  // 指向简单打印机实例指针的指针
    (*p)->print(p1, "p1"); // 调用打印函数打印字符串

    p = (printer_t **)p2;  // 指向简单打印机实例指针的指针
    (*p)->print(p2, "p2"); // 调用打印函数打印字符串

    p = (printer_t **)c1;  // 指向彩色打印机实例指针的指针
    (*p)->print(c1, "c1"); // 调用打印函数打印字符串

    p = (printer_t **)c2;  // 指向彩色打印机实例指针的指针
    (*p)->print(c2, "c2"); // 调用打印函数打印字符串

    plain_printer_delete(p1); // 删除简单打印机实例
    plain_printer_delete(p2); // 删除简单打印机实例
    color_printer_delete(c1); // 删除彩色打印机实例
    color_printer_delete(c2); // 删除彩色打印机实例
}
