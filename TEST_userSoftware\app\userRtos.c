/********************************************************************************
 *@Description    :
 *@Autor          : Li
 *@Date           : 2023-06-08 20:42:09
 *@LastEditors    : Li
 ******************************************************************************/
#include "app_head.h"
#include "mid_head.h"
#include "utils.h" // 包含时间转换函数
#include <time.h>

// 静态法，64个容器待使用，使用static修饰的话debug麻烦,类比成64个任务堆栈
userContainer_t userContainerBuf[64];
static uint16_t listStaticMaxNum = 0;

// 定义两个链表
static userList_t delayList;
static userList_t readyList;

/********************************************************************************
 *@description:链表初始化
 *@param {userList_t} *ListParam
 *@return {*}
 *@author: Li
 ******************************************************************************/
static void ListInit(userList_t* ListParam) {
    // 索引开始指向链表尾部
    ListParam->index = (userListItem_t*)(&(ListParam->lastItem));
    // 链表中节点个数为零
    ListParam->numberOfItems = 0;
    // 唯一一个节点，指向自己
    ListParam->index->Previous = ListParam->index;
    ListParam->index->Next = ListParam->index;
    // 头指针，就是尾指针，值最大的话方便后续插入操作
    ListParam->index->xItemValue = MAXDATA;
}

/********************************************************************************
 *@description:节点初始化，空载就行，不挂在任何的链表之下
 *@param {userListItem_t} *ListItemParam
 *@return {*}
 *@author: Li
 ******************************************************************************/
static void ListItemInit(userListItem_t* ListItemParam) {
    ListItemParam->whatList = (userList_t*)NULL;
}

/********************************************************************************
 *@description:
 *@return {*}
 *@author: Li
 ******************************************************************************/
static void ListItemInsertEnd(userContainer_t* whatContainer, userList_t* ListParam, uint32_t ListItemValue) {
    // 从创建的容器中找到节点
    userListItem_t* ListItemParam = &(whatContainer->Item);

    userListItem_t* ListItemEnd = ListParam->index;
    //  开辟的空间只有64，多余就加不上了
    if (listStaticMaxNum <= 64)
        listStaticMaxNum++;
    else
        return;
    // 给节点赋值
    ListItemParam->xItemValue = ListItemValue;
    whatContainer->ContainerValue = ListItemValue + 1;
    // 链表节点数加1
    ListParam->numberOfItems++;
    // 该节点属于哪个链表
    ListItemParam->whatList = (userList_t*)ListParam;
    // 该节点属于哪个容器
    ListItemParam->contents = (userContainer_t*)ListItemParam;

    //  原来前面指向ListItemEnd的指针现在指向新加的
    ListItemParam->Next = ListItemEnd;
    ListItemParam->Previous = ListItemEnd->Previous;

    ListItemEnd->Previous->Next = ListItemParam;
    ListItemEnd->Previous = ListItemParam;
}

/********************************************************************************
 *@description:在轮询创造十个节点以后，随便在64后面找一个节点，赋值xItemValue小于10
 容器的值不等于xItemValue，测试是否插入成功
 *@return {*}
 *@author: Li
 ******************************************************************************/
__USED static void ListItemInsertValue(userContainer_t* whatContainer, userList_t* ListParam, uint32_t ListItemValue) {
    userListItem_t* ListItemParam = &(whatContainer->Item);
    // 找到链表第一个元素
    userListItem_t* ListItemCmp = ListParam->lastItem.Next;

    userListItem_t* ListItemIndex = ListParam->index;

    if (listStaticMaxNum <= 64)
        listStaticMaxNum++;
    else
        return;
    // 比较找到插入的位置
    for (; ListItemParam->xItemValue < ListItemCmp->xItemValue; ListItemCmp = ListItemCmp->Next) {
        // 更新链表索引位置
        ListItemIndex = ListItemCmp;
    }
    // 插入数据
}

__USED static void ListItemInsertRemove(userListItem_t* ListItemParam) {
    userList_t* ListParam = ListItemParam->whatList;
    // 链表节点数减1
    ListParam->numberOfItems--;
    // 该节点从链表中移除
    ListItemParam->Previous->Next = ListItemParam->Next;

    ListItemParam->Next->Previous = ListItemParam->Previous;
    // 该节点归零
    ListItemInit(ListItemParam);
}

static void ListTestTask(void) {
    uint8_t i;
    /*方便操作*/
    userListItem_t* delayList_points;

    /*格式化内存*/
    USER_PRINTF("sizeof:%d\n", sizeof(userContainerBuf));
    memset(userContainerBuf, 0x00, sizeof(userContainerBuf));
    /*初始化链表*/
    ListInit(&delayList);
    ListInit(&readyList);
    /*节点赋值*/
    for (i = 0; i < 10; i++) {
        userContainerBuf[i].ContainerValue = i;
        ListItemInsertEnd(&userContainerBuf[i], &delayList, i);
    }

    delayList_points = &(userContainerBuf[0].Item);

    for (i = 0; i < 10; i++) {
        USER_PRINTF("ContainerValue:%d\r\n", ((userContainer_t*)delayList_points->contents)->ContainerValue);
        USER_PRINTF("xItemValue:%d\r\n", delayList_points->xItemValue);
        delayList_points = delayList_points->Next;
    }
}

// 定义一个学生结构体
struct userRtos_s {
    uint8_t taskNum;
    TaskHandle_t taskHandle[10];
};
// 定义一个RTOS结构体
#define USER_RTOS_TASK_NUM 1
// 定义任务个数
enum AnimalType {
    ANIMAL_TYPE_DOG,
    ANIMAL_TYPE_CAT,
    ANIMAL_TYPE_BIRD,
};

void ListTask(void* argument) {
    ListTestTask();   // 链表测试
    AnimalTestTask(); // 继承测试
    test_print();     // 多态测试

    /******************************************************** */
    struct lsd light1 = {0x00};
    struct led light2 = {0x00};

    lsd_init(&light1);
    led_init(&light2);

    (*((struct light_i**)&light1))->on(&light1);
    light1.interface->off(&light1);

    light_on(&light1);
    light_on(&light2);
    light_off(&light1);
    light_off(&light2);

    /******************************************************** */
    student_f.init(&student_f, 18, "nie");
    student_f.print(&student_f);
    student_f.setAge(&student_f, 20);
    student_f.setName(&student_f, "Li");
    student_f.print(&student_f);

    student_t* p_stu = &student_f;
    p_stu->setAge(&student_f, 30);
    p_stu->print(&student_f);

    /******************************************************** */
    uint8_t time[6] = {2025 - 1970, 8, 17, 21, 47, 10};
    uint8_t time_buf[6] = {0x00};
    uint32_t timestamp = 0x00;

    timestamp = time_to_timestamp(time);

    timestamp_to_time(timestamp, time_buf);

    if (memcmp(time, time_buf, 6) == 0) {
        USER_PRINTF("time conversion test pass\r\n");
    }

    /******************************************************** */
    uint8_t dlt645_heart[] = {0x68, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x68,
                              0x11, 0x04, 0x33, 0x34, 0x35, 0x36, 0x00, 0x16};

    for (size_t i = 0; i < sizeof(dlt645_heart) - 2; i++) {
        dlt645_heart[sizeof(dlt645_heart) - 2] += dlt645_heart[i];
    }

    while (1) {
        vTaskDelay(10 * 1000);
        DebugITSendBuf(dlt645_heart, sizeof(dlt645_heart));
    }
}
