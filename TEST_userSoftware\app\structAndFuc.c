/*
 * @Author: li
 * @Date: 2024-07-21 16:24:46
 * @LastEditTime: 2025-06-23 22:23:30
 * @Description:
 */

/******************************************************************************
 *@Description    :
 *@Autor          : Li
 *@Date           : 2024-07-21 16:24:46
 * @LastEditors: Please set LastEditors
 ******************************************************************************/

#include "app_head.h"
#include "mid_head.h"

typedef struct stuPrivate {
    int age;
    char name[20];

} stuPrivate_t;

#define STUDENT_PRIVATE(this) ((stuPrivate_t*)(this->innerData))

static void student_createAndArgs(student_t* this, int age, const char* recvName);
static void student_createDefault(student_t* this);
static void student_delete(student_t* this);
static void student_print(student_t* this);
static int student_getAge(student_t* this);
static void student_setAge(student_t* this, int age);
static const char* student_getName(student_t* this);
static void student_setName(student_t* this, const char* name);

/******************************************************************************
 *@description:
 *@param {void} *arg
 *@return {*}
 *@author: Li
 ******************************************************************************/

static void student_delete(student_t* this) {
    USER_FREE(STUDENT_PRIVATE(this));
    USER_FREE(this);
}

static void student_print(student_t* this) {
    USER_PRINTF("Name: %s, Age: %d\r\n", STUDENT_PRIVATE(this)->name, STUDENT_PRIVATE(this)->age);
}

static int student_getAge(student_t* this) {
    return STUDENT_PRIVATE(this)->age;
}

static void student_setAge(student_t* this, int age) {
    STUDENT_PRIVATE(this)->age = age;
}

static const char* student_getName(student_t* this) {
    return STUDENT_PRIVATE(this)->name;
}

static void student_setName(student_t* this, const char* name) {
    strcpy(STUDENT_PRIVATE(this)->name, name);
}

static void student_createDefault(student_t* this) {
    student_createAndArgs(this, 18, "Li");
}

static stuPrivate_t stuPrivate;

static void student_createAndArgs(student_t* this, int age, const char* recvName) {
    this->innerData = &stuPrivate;

    STUDENT_PRIVATE(this)->age = age;
    strcpy(STUDENT_PRIVATE(this)->name, recvName);
    this->createDefault = student_createDefault;
    this->print = student_print;
    this->delete_ = student_delete;
    this->getName = student_getName;
    this->setName = student_setName;
    this->getAge = student_getAge;
    this->setAge = student_setAge;
    this->delete_ = student_delete;

    USER_PRINTF("sizeof stuPrivate_t:%d\r\n", sizeof(stuPrivate_t));
}

student_t student_f = {
    .init = student_createAndArgs,
};
