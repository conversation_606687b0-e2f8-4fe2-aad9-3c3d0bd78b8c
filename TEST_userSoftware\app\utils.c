#include "utils.h"

#define is_leap_year(year) (((year) % 4 == 0 && (year) % 100 != 0) || ((year) % 400 == 0))

/// 写一个时间转换成时间戳的函数
uint32_t time_to_timestamp(uint8_t* time) {
    // time[0]: year offset from 1970, time[1]: month (1-12), time[2]: day (1-31), time[3]: hour, time[4]: min, time[5]: sec
    static const uint8_t days_in_month[12] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    uint32_t timestamp = 0;
    uint16_t year = 1970 + time[0];
    uint8_t month = time[1];
    uint8_t day = time[2];

    // Add seconds for each year from 1970 to current year (inclusive)
    for (uint16_t y = 1970; y < year; y++) {
        timestamp += is_leap_year(y) ? 366UL * 24 * 3600 : 365UL * 24 * 3600;
    }

    // Add seconds for each month in current year (up to previous month)
    for (uint8_t m = 1; m < month; m++) {
        if (m == 2 && is_leap_year(year)) {
            timestamp += 29UL * 24 * 3600;
        } else {
            timestamp += (uint32_t)days_in_month[m - 1] * 24 * 3600;
        }
    }

    // Add seconds for days (full days), hours, minutes, seconds
    timestamp += (uint32_t)(day) * 24 * 3600;  // Fixed: use day instead of day-1
    timestamp += (uint32_t)time[3] * 3600;
    timestamp += (uint32_t)time[4] * 60;
    timestamp += (uint32_t)time[5];

    // Adjust for Beijing timezone (UTC+8)
    timestamp -= 8 * 3600;

    return timestamp;
}

void timestamp_to_time(uint32_t timestamp, uint8_t* time) {
    // Add Beijing timezone offset (UTC+8)
    timestamp += 8 * 3600;

    // Convert timestamp back to time structure
    uint32_t seconds_in_a_day = 24 * 3600;  // seconds in a day
    uint32_t days = timestamp / seconds_in_a_day; // total days
    timestamp %= seconds_in_a_day; // remaining seconds

    time[5] = timestamp % 60; // seconds
    timestamp /= 60;
    time[4] = timestamp % 60; // minutes
    timestamp /= 60;
    time[3] = timestamp % 24; // hours

    // Calculate days, months, years
    uint16_t year = 1970;
    while (days >= (is_leap_year(year) ? 366 : 365)) {
        days -= (is_leap_year(year) ? 366 : 365);
        year++;
    }
    time[0] = year - 1970; // year offset
    uint8_t month = 1;
    static const uint8_t days_in_month[12] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    while (days >= (month == 2 && is_leap_year(year) ? 29 : days_in_month[month - 1])) {
        days -= (month == 2 && is_leap_year(year) ? 29 : days_in_month[month - 1]);
        month++;
    }
    time[1] = month;    // month
    time[2] = days + 1; // day
}

#pragma diag_suppress=Pe223

// Test function
void test_time_conversion(void) {
    uint8_t test_cases[][6] = {
        {0, 1, 1, 0, 0, 0},       // 1970-01-01 00:00:00
        {0, 1, 1, 12, 30, 45},    // 1970-01-01 12:30:45
        {0, 2, 28, 23, 59, 59},   // 1970-02-28 23:59:59
        {0, 3, 1, 0, 0, 0},       // 1970-03-01 00:00:00
        {2, 2, 29, 15, 30, 0},    // 1972-02-29 15:30:00 (闰年)
        {30, 12, 31, 23, 59, 59}, // 2000-12-31 23:59:59
        {53, 8, 16, 12, 0, 0}     // 2023-08-16 12:00:00
    };

    printf("\nStarting time conversion tests...\n\n");

    for (int i = 0; i < sizeof(test_cases) / sizeof(test_cases[0]); i++) {
        uint8_t input_time[6];
        uint8_t output_time[6];

        // 复制测试用例
        for (int j = 0; j < 6; j++) {
            input_time[j] = test_cases[i][j];
        }

        // 转换测试
        uint32_t timestamp = time_to_timestamp(input_time);
        timestamp_to_time(timestamp, output_time);

        // 打印结果
        printf("Test case %d:\n", i + 1);
        printf("Input:  %d-%02d-%02d %02d:%02d:%02d\n", 1970 + input_time[0], input_time[1], input_time[2],
               input_time[3], input_time[4], input_time[5]);
        printf("Output: %d-%02d-%02d %02d:%02d:%02d\n", 1970 + output_time[0], output_time[1], output_time[2],
               output_time[3], output_time[4], output_time[5]);
        printf("Timestamp: %u\n", timestamp);

        // 验证结果
        int match = 1;
        for (int j = 0; j < 6; j++) {
            if (input_time[j] != output_time[j]) {
                match = 0;
                break;
            }
        }
        printf("Result: %s\n\n", match ? "PASS" : "FAIL");
    }
}
