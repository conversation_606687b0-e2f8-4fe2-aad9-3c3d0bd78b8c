/*
 * @Author: li
 * @Date: 2025-08-10 17:55:08
 * @LastEditTime: 2025-08-10 22:06:13
 * @Description: 
 */
#include "W25Q128_flash.h"

// W25Q 指令
#define CMD_READ_ID        0x90
#define CMD_READ_DATA      0x03
#define CMD_PAGE_PROGRAM   0x02
#define CMD_SECTOR_ERASE   0x20
#define CMD_BLOCK_ERASE_64K 0xD8
#define CMD_WRITE_ENABLE   0x06
#define CMD_READ_STATUS1   0x05

inline static void W25Q_CS_Low(void) {
    HAL_GPIO_WritePin(W25Q_CS_PORT, W25Q_CS_PIN, GPIO_PIN_RESET);
}

inline static void W25Q_CS_High(void) {
    HAL_GPIO_WritePin(W25Q_CS_PORT, W25Q_CS_PIN, GPIO_PIN_SET);
}

static void W25Q_WriteEnable(void) {
    uint8_t cmd = CMD_WRITE_ENABLE;
    W25Q_CS_Low();
    HAL_SPI_Transmit(W25Q_SPI, &cmd, 1, HAL_MAX_DELAY);
    W25Q_CS_High();
}

static void W25Q_WaitBusy(void) {
    uint8_t cmd = CMD_READ_STATUS1;
    uint8_t status;
    do {
        W25Q_CS_Low();
        HAL_SPI_Transmit(W25Q_SPI, &cmd, 1, HAL_MAX_DELAY);
        HAL_SPI_Receive(W25Q_SPI, &status, 1, HAL_MAX_DELAY);
        W25Q_CS_High();
    } while (status & 0x01);
}

static void W25Q_ReadID(uint8_t *mid, uint8_t *did) {
    uint8_t cmd[4] = {CMD_READ_ID, 0x00, 0x00, 0x00};
    uint8_t id[2];
    W25Q_CS_Low();
    HAL_SPI_Transmit(W25Q_SPI, cmd, 4, HAL_MAX_DELAY);
    HAL_SPI_Receive(W25Q_SPI, id, 2, HAL_MAX_DELAY);
    W25Q_CS_High();
    *mid = id[0];
    *did = id[1];
}

static int W25Q_Init(void) {
    uint8_t mid, did;
    W25Q_ReadID(&mid, &did);
    return (mid == 0xEF);
}

static int W25Q_ReadData(uint32_t addr, uint8_t *buf, uint32_t len) {
    uint8_t cmd[4] = {CMD_READ_DATA, addr >> 16, addr >> 8, addr};
    W25Q_CS_Low();
    HAL_SPI_Transmit(W25Q_SPI, cmd, 4, HAL_MAX_DELAY);
    HAL_SPI_Receive(W25Q_SPI, buf, len, HAL_MAX_DELAY);
    W25Q_CS_High();
    return 0;
}

static int W25Q_PageProgram(uint32_t addr, const uint8_t *buf, uint32_t len) {
    if (len > W25Q_PAGE_SIZE) return -1;
    W25Q_WriteEnable();
    uint8_t cmd[4] = {CMD_PAGE_PROGRAM, addr >> 16, addr >> 8, addr};
    W25Q_CS_Low();
    HAL_SPI_Transmit(W25Q_SPI, cmd, 4, HAL_MAX_DELAY);
    HAL_SPI_Transmit(W25Q_SPI, (uint8_t*)buf, len, HAL_MAX_DELAY);
    W25Q_CS_High();
    W25Q_WaitBusy();
    return 0;
}

static int W25Q_EraseSector(uint32_t addr) {
    W25Q_WriteEnable();
    uint8_t cmd[4] = {CMD_SECTOR_ERASE, addr >> 16, addr >> 8, addr};
    W25Q_CS_Low();
    HAL_SPI_Transmit(W25Q_SPI, cmd, 4, HAL_MAX_DELAY);
    W25Q_CS_High();
    W25Q_WaitBusy();
    return 0;
}

static int W25Q_EraseBlock64K(uint32_t addr) {
    W25Q_WriteEnable();
    uint8_t cmd[4] = {CMD_BLOCK_ERASE_64K, addr >> 16, addr >> 8, addr};
    W25Q_CS_Low();
    HAL_SPI_Transmit(W25Q_SPI, cmd, 4, HAL_MAX_DELAY);
    W25Q_CS_High();
    W25Q_WaitBusy();
    return 0;
}

static W25Q128_flash_t W25Q = {
    .check_id = W25Q_Init,
    .init = W25Q_Init,
    .read = W25Q_ReadData,
    .write = W25Q_PageProgram,
    .erase_4k = W25Q_EraseSector,
    .erase_block64k = W25Q_EraseBlock64K
};

W25Q128_flash_t *W25Q_Flash = &W25Q;