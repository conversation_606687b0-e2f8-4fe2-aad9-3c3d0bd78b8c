###############################################################################
#
# IAR ELF Linker V9.40.1.364/W64 for ARM                  17/Aug/2025  21:47:44
# Copyright 2007-2023 IAR Systems AB.
#
#    Output file  =
#        D:\routine\stm32f1_software\software_All_example\compileFile\Exe\TEST_bootloader.out
#    Map file     =
#        D:\routine\stm32f1_software\software_All_example\compileFile\List\TEST_bootloader.map
#    Command line =
#        -f
#        D:\routine\stm32f1_software\software_All_example\compileFile\Exe\TEST_bootloader.out.rsp
#        (D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\animal.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\mid_1628371642073116930.dir\backUartMid.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\cmsis_os2.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\croutine.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\dataSerialization.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\debugApp.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\mid_1628371642073116930.dir\debugUartMid.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir\dma.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\e2prom_24c02.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\event_groups.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir\freertos.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir\gpio.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\heap_4.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir\i2c.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\light_fn_18089876452921840385.dir\led.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\light_fn_18089876452921840385.dir\light.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\list.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\light_fn_18089876452921840385.dir\lsd.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir\main.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\port.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\portasm.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\print.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\queue.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\ringbuffer.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\sfud_6715763213700909193.dir\sfud.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\sfud_6715763213700909193.dir\sfud_port.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\sfud_6715763213700909193.dir\sfud_sfdp.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir\spi.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\spi_nor_time_series.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\EWARM_18443280873093131863.dir\startup_stm32f103xe.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_i2c.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir\stm32f1xx_hal_msp.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir\stm32f1xx_hal_timebase_tim.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir\stm32f1xx_it.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\stream_buffer.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\structAndFuc.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\CMSIS_6603591812247902717.dir\system_stm32f1xx.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\sysTickApp.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\tasks.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\timers.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\uplinkEsp8266App.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\mid_1628371642073116930.dir\uplinkUartMid.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir\usart.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\userRtos.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\utils.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\W25Q128_flash.o
#        --no_out_extension -o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Exe\TEST_bootloader.out
#        --redirect _Printf=_PrintfFullNoMb --redirect _Scanf=_ScanfFullNoMb
#        --map
#        D:\routine\stm32f1_software\software_All_example\compileFile\List\TEST_bootloader.map
#        --config
#        D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/stm32f103xe_flash.icf
#        --semihosting --entry __iar_program_start --vfe --text_out locale
#        --cpu=Cortex-M3 --fpu=None) --dependencies=n
#        D:\routine\stm32f1_software\software_All_example\compileFile\Exe\TEST_bootloader.out.iar_deps
#
###############################################################################

*******************************************************************************
*** RUNTIME MODEL ATTRIBUTES
***

CppFlavor       = *
__CPP_Runtime   = 1
__SystemLibrary = DLib
__dlib_version  = 6


*******************************************************************************
*** HEAP SELECTION
***

The basic heap was selected because --advanced_heap
was not specified and the application did not appear to
be primarily optimized for speed.


*******************************************************************************
*** PLACEMENT SUMMARY
***

"A0":  place at address 0x800'0000 { ro section .intvec };
"P1":  place in [from 0x800'0000 to 0x807'ffff] { ro };
define block CSTACK with size = 4K, alignment = 8 { };
define block HEAP with size = 512, alignment = 8 { };
"P2":  place in [from 0x2000'0000 to 0x2000'ffff] {
          rw, block CSTACK, block HEAP };
initialize by copy { rw };

  Section            Kind         Address    Size  Object
  -------            ----         -------    ----  ------
"A0":                                       0x130
  .intvec            ro code   0x800'0000   0x130  startup_stm32f103xe.o [3]
                             - 0x800'0130   0x130

"P1":                                      0x6dcf
  .text              ro code   0x800'0130   0xeca  xprintffull_nomb.o [10]
  .text              ro code   0x800'0ffa    0x3a  packbits_init_single.o [12]
  .text              ro code   0x800'1034    0x16  strchr.o [12]
  .text              ro code   0x800'104a    0x1a  xsnprout.o [10]
  .text              ro code   0x800'1064    0x40  xfail_s.o [10]
  .text              ro code   0x800'10a4    0x36  strlen.o [12]
  .rodata            const     0x800'10da     0x2  stm32f1xx_hal_rcc.o [5]
  .text              ro code   0x800'10dc    0x58  memchr.o [12]
  .text              ro code   0x800'1134    0xa6  ABImemcpy.o [12]
  .rodata            const     0x800'11da     0x2  xlocale_c.o [10]
  .text              ro code   0x800'11dc    0x70  frexp.o [11]
  .text              ro code   0x800'124c    0x34  DblCmpLe.o [11]
  .text              ro code   0x800'1280    0x34  DblCmpGe.o [11]
  .text              ro code   0x800'12b4   0x13a  ldexp.o [11]
  .rodata            const     0x800'13ee     0x1  xlocale_c.o [10]
  .text              ro code   0x800'13f0    0x36  DblToS32.o [11]
  .text              ro code   0x800'1428    0x22  S32ToDbl.o [11]
  .text              ro code   0x800'144c   0x24e  DblAddSub.o [11]
  .text              ro code   0x800'169c   0x246  DblDiv.o [11]
  .text              ro code   0x800'18e4    0x22  DblToU32.o [11]
  .text              ro code   0x800'1908    0x1c  U32ToDbl.o [11]
  .text              ro code   0x800'1924   0x1aa  DblMul.o [11]
  .text              ro code   0x800'1ad0     0x8  xlocale_c.o [10]
  .text              ro code   0x800'1ad8   0x230  I64DivMod.o [12]
  .text              ro code   0x800'1d08     0x6  abort.o [10]
  .text              ro code   0x800'1d10     0x2  I64DivZer.o [12]
  .text              ro code   0x800'1d14    0x14  exit.o [13]
  .text              ro code   0x800'1d28    0x2c  iarttio.o [13]
  .text              ro code   0x800'1d54     0x8  XShttio.o [10]
  .text              ro code   0x800'1d5c   0xa54  tasks.o [4]
  .text              ro code   0x800'27b0   0x278  heap_4.o [4]
  .text              ro code   0x800'2a28    0x66  ABImemset.o [12]
  .text              ro code   0x800'2a8e    0x9a  list.o [4]
  .text              ro code   0x800'2b28   0x1d0  port.o [4]
  .text              ro code   0x800'2cf8   0x1b8  cmsis_os2.o [4]
  .text              ro code   0x800'2eb0   0x478  timers.o [4]
  .text              ro code   0x800'3328    0x34  port.o [4]
  CODE               ro code   0x800'335c    0x88  portasm.o [4]
  .text              ro code   0x800'33e4   0x770  queue.o [4]
  .text              ro code   0x800'3b54   0x694  stm32f1xx_hal_rcc.o [5]
  .text              ro code   0x800'41e8     0xc  stm32f1xx_hal.o [5]
  .text              ro code   0x800'41f4    0x94  stm32f1xx_hal_timebase_tim.o [2]
  .text              ro code   0x800'4288   0x10c  stm32f1xx_hal_cortex.o [5]
  .text              ro code   0x800'4394   0x324  stm32f1xx_hal_tim.o [5]
  .text              ro code   0x800'46b8     0x2  stm32f1xx_hal_tim.o [5]
  .text              ro code   0x800'46ba     0x2  stm32f1xx_hal_tim.o [5]
  .text              ro code   0x800'46bc     0x2  stm32f1xx_hal_tim.o [5]
  .text              ro code   0x800'46be     0x2  stm32f1xx_hal_tim.o [5]
  .text              ro code   0x800'46c0    0xdc  main.o [2]
  .text              ro code   0x800'479c     0x2  stm32f1xx_hal_tim_ex.o [5]
  .text              ro code   0x800'479e     0x2  stm32f1xx_hal_tim.o [5]
  .text              ro code   0x800'47a0     0x2  stm32f1xx_hal_tim_ex.o [5]
  .text              ro code   0x800'47a4    0x24  stm32f1xx_hal.o [5]
  .text              ro code   0x800'47c8    0x7c  gpio.o [2]
  .text              ro code   0x800'4844    0x4c  dma.o [2]
  .text              ro code   0x800'4890   0x2ec  usart.o [2]
  .text              ro code   0x800'4b7c    0xcc  spi.o [2]
  .text              ro code   0x800'4c48    0xa8  i2c.o [2]
  .text              ro code   0x800'4cf0   0x18c  debugApp.o [6]
  .text              ro code   0x800'4e7c    0x74  freertos.o [2]
  .text              ro code   0x800'4ef0    0x28  stm32f1xx_hal.o [5]
  .text              ro code   0x800'4f18    0x18  stm32f1xx_hal.o [5]
  .text              ro code   0x800'4f30    0x54  stm32f1xx_hal_msp.o [2]
  .text              ro code   0x800'4f84    0x14  memset.o [12]
  .text              ro code   0x800'4f98   0x2a0  stm32f1xx_hal_gpio.o [5]
  .text              ro code   0x800'5238   0x540  stm32f1xx_hal_uart.o [5]
  .text              ro code   0x800'5778   0x66c  stm32f1xx_hal_dma.o [5]
  .text              ro code   0x800'5de4    0xc4  stm32f1xx_hal_spi.o [5]
  .text              ro code   0x800'5ea8   0x1ec  stm32f1xx_hal_i2c.o [5]
  .text              ro code   0x800'6094    0x3c  vsnprint.o [10]
  .text              ro code   0x800'60d0     0x2  stm32f1xx_hal_uart.o [5]
  .text              ro code   0x800'60d2     0x2  stm32f1xx_hal_uart.o [5]
  .text              ro code   0x800'60d4     0x2  stm32f1xx_hal_uart.o [5]
  .text              ro code   0x800'60d6     0x2  stm32f1xx_hal_uart.o [5]
  .text              ro code   0x800'60d8   0x28c  userRtos.o [6]
  .text              ro code   0x800'6364    0x70  animal.o [6]
  .text              ro code   0x800'63d4   0x190  print.o [6]
  .text              ro code   0x800'6564    0x44  lsd.o [7]
  .text              ro code   0x800'65a8    0x44  led.o [7]
  .text              ro code   0x800'65ec   0x268  utils.o [6]
  .text              ro code   0x800'6854    0x68  memcmp.o [12]
  .text              ro code   0x800'68bc    0x70  strncpy.o [12]
  .text              ro code   0x800'692c    0xc8  structAndFuc.o [6]
  .text              ro code   0x800'69f4    0x18  strcpy.o [12]
  .text              ro code   0x800'6a0c    0x74  stm32f1xx_it.o [2]
  .text              ro code   0x800'6a80    0x38  zero_init3.o [12]
  .text              ro code   0x800'6ab8    0x28  data_init.o [12]
  .rodata            const     0x800'6ae0    0x24  freertos.o [2]
  .rodata            const     0x800'6b04    0x24  freertos.o [2]
  .iar.init_table    const     0x800'6b28    0x20  - Linker created -
  .text              ro code   0x800'6b48    0x1e  cmain.o [12]
  .text              ro code   0x800'6b66     0x4  low_level_init.o [10]
  .text              ro code   0x800'6b6a     0x4  exit.o [10]
  .text              ro code   0x800'6b70     0xa  cexit.o [12]
  .rodata            const     0x800'6b7c    0x1c  structAndFuc.o [6]
  .rodata            const     0x800'6b98    0x1c  userRtos.o [6]
  .text              ro code   0x800'6bb4    0x1c  cstartup_M.o [12]
  .rodata            const     0x800'6bd0    0x18  animal.o [6]
  .rodata            const     0x800'6be8    0x14  animal.o [6]
  .rodata            const     0x800'6bfc    0x14  animal.o [6]
  .rodata            const     0x800'6c10    0x14  animal.o [6]
  .rodata            const     0x800'6c24    0x14  freertos.o [2]
  .rodata            const     0x800'6c38    0x14  structAndFuc.o [6]
  .rodata            const     0x800'6c4c    0x14  userRtos.o [6]
  .rodata            const     0x800'6c60    0x10  print.o [6]
  .rodata            const     0x800'6c70    0x10  stm32f1xx_hal_rcc.o [5]
  .rodata            const     0x800'6c80    0x10  system_stm32f1xx.o [1]
  .rodata            const     0x800'6c90    0x10  userRtos.o [6]
  .rodata            const     0x800'6ca0    0x10  userRtos.o [6]
  .text              ro code   0x800'6cb0    0x10  startup_stm32f103xe.o [3]
  .rodata            const     0x800'6cc0     0xc  freertos.o [2]
  .rodata            const     0x800'6ccc     0xc  freertos.o [2]
  .rodata            const     0x800'6cd8     0xc  led.o [7]
  .rodata            const     0x800'6ce4     0xc  lsd.o [7]
  .rodata            const     0x800'6cf0     0xc  print.o [6]
  .rodata            const     0x800'6cfc     0xc  print.o [6]
  .rodata            const     0x800'6d08     0xc  userRtos.o [6]
  .rodata            const     0x800'6d14     0xc  utils.o [6]
  .rodata            const     0x800'6d20     0xc  utils.o [6]
  .rodata            const     0x800'6d2c     0x8  animal.o [6]
  .rodata            const     0x800'6d34     0x8  animal.o [6]
  .rodata            const     0x800'6d3c     0x8  led.o [7]
  .rodata            const     0x800'6d44     0x8  lsd.o [7]
  .rodata            const     0x800'6d4c     0x8  main.o [2]
  .rodata            const     0x800'6d54     0x8  main.o [2]
  .rodata            const     0x800'6d5c     0x8  print.o [6]
  .rodata            const     0x800'6d64     0x8  print.o [6]
  .rodata            const     0x800'6d6c     0x8  print.o [6]
  .rodata            const     0x800'6d74     0x8  print.o [6]
  .rodata            const     0x800'6d7c     0x8  print.o [6]
  .rodata            const     0x800'6d84     0x8  system_stm32f1xx.o [1]
  .rodata            const     0x800'6d8c     0x8  tasks.o [4]
  .rodata            const     0x800'6d94     0x8  timers.o [4]
  .rodata            const     0x800'6d9c     0x8  timers.o [4]
  .rodata            const     0x800'6da4     0x8  userRtos.o [6]
  .rodata            const     0x800'6dac     0x8  userRtos.o [6]
  .rodata            const     0x800'6db4     0x8  userRtos.o [6]
  .rodata            const     0x800'6dbc     0x4  heap_4.o [4]
  .rodata            const     0x800'6dc0     0x4  port.o [4]
  .rodata            const     0x800'6dc4     0x4  print.o [6]
  .rodata            const     0x800'6dc8     0x4  print.o [6]
  .text              ro code   0x800'6dcc     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6dd0     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6dd4     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6dd8     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6ddc     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6de0     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6de4     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6de8     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6dec     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6df0     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6df4     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6df8     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6dfc     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e00     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e04     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e08     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e0c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e10     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e14     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e18     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e1c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e20     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e24     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e28     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e2c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e30     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e34     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e38     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e3c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e40     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e44     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e48     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e4c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e50     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e54     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e58     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e5c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e60     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e64     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e68     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e6c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e70     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e74     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e78     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e7c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e80     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e84     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e88     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e8c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e90     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e94     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e98     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6e9c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'6ea0     0x2  system_stm32f1xx.o [1]
  .rodata            const     0x800'6ea2     0x0  zero_init3.o [12]
  .rodata            const     0x800'6ea2     0x0  packbits_init_single.o [12]
  Initializer bytes  const     0x800'6ea2    0x5d  <for P2-1>
                             - 0x800'6eff  0x6dcf

"P2", part 1 of 3:                           0x89
  P2-1                        0x2000'0000    0x89  <Init block>
    .data            inited   0x2000'0000     0xc  led.o [7]
    .data            inited   0x2000'000c     0xc  lsd.o [7]
    .data            inited   0x2000'0018     0x4  port.o [4]
    .data            inited   0x2000'001c     0x4  stm32f1xx_hal.o [5]
    .data            inited   0x2000'0020    0x24  structAndFuc.o [6]
    .data            inited   0x2000'0044     0x4  system_stm32f1xx.o [1]
    .data            inited   0x2000'0048    0x38  xlocale_c.o [10]
    .data            inited   0x2000'0080     0x8  XShttio.o [10]
    .data            inited   0x2000'0088     0x1  stm32f1xx_hal.o [5]
                            - 0x2000'0089    0x89

"P2", part 2 of 3:                         0x9c68
  .bss               zero     0x2000'008c  0x7800  heap_4.o [4]
  .bss               zero     0x2000'788c   0x808  debugApp.o [6]
  .bss               zero     0x2000'8094   0x600  userRtos.o [6]
  .bss               zero     0x2000'8694   0x460  tasks.o [4]
  .bss               zero     0x2000'8af4   0x400  cmsis_os2.o [4]
  .bss               zero     0x2000'8ef4   0x400  debugApp.o [6]
  .bss               zero     0x2000'92f4   0x200  cmsis_os2.o [4]
  .bss               zero     0x2000'94f4   0x200  debugApp.o [6]
  .bss               zero     0x2000'96f4    0xa0  timers.o [4]
  .bss               zero     0x2000'9794    0x6c  cmsis_os2.o [4]
  .bss               zero     0x2000'9800    0x6c  cmsis_os2.o [4]
  .bss               zero     0x2000'986c    0x58  spi.o [2]
  .bss               zero     0x2000'98c4    0x54  i2c.o [2]
  .bss               zero     0x2000'9918    0x50  timers.o [4]
  .bss               zero     0x2000'9968    0x48  stm32f1xx_hal_timebase_tim.o [2]
  .bss               zero     0x2000'99b0    0x44  usart.o [2]
  .bss               zero     0x2000'99f4    0x44  usart.o [2]
  .bss               zero     0x2000'9a38    0x44  usart.o [2]
  .bss               zero     0x2000'9a7c    0x44  usart.o [2]
  .bss               zero     0x2000'9ac0    0x44  usart.o [2]
  .bss               zero     0x2000'9b04    0x44  usart.o [2]
  .bss               zero     0x2000'9b48    0x40  queue.o [4]
  .bss               zero     0x2000'9b88    0x18  structAndFuc.o [6]
  .bss               zero     0x2000'9ba0    0x14  tasks.o [4]
  .bss               zero     0x2000'9bb4    0x14  tasks.o [4]
  .bss               zero     0x2000'9bc8    0x14  tasks.o [4]
  .bss               zero     0x2000'9bdc    0x14  tasks.o [4]
  .bss               zero     0x2000'9bf0    0x14  tasks.o [4]
  .bss               zero     0x2000'9c04    0x14  timers.o [4]
  .bss               zero     0x2000'9c18    0x14  timers.o [4]
  .bss               zero     0x2000'9c2c    0x14  userRtos.o [6]
  .bss               zero     0x2000'9c40    0x14  userRtos.o [6]
  .bss               zero     0x2000'9c54    0x10  debugApp.o [6]
  .bss               zero     0x2000'9c64     0xc  dataSerialization.o [6]
  .bss               zero     0x2000'9c70     0x8  heap_4.o [4]
  .bss               zero     0x2000'9c78     0x4  cmsis_os2.o [4]
  .bss               zero     0x2000'9c7c     0x4  freertos.o [2]
  .bss               zero     0x2000'9c80     0x4  freertos.o [2]
  .bss               zero     0x2000'9c84     0x4  heap_4.o [4]
  .bss               zero     0x2000'9c88     0x4  heap_4.o [4]
  .bss               zero     0x2000'9c8c     0x4  heap_4.o [4]
  .bss               zero     0x2000'9c90     0x4  heap_4.o [4]
  .bss               zero     0x2000'9c94     0x4  port.o [4]
  .bss               zero     0x2000'9c98     0x4  stm32f1xx_hal.o [5]
  .bss               zero     0x2000'9c9c     0x4  tasks.o [4]
  .bss               zero     0x2000'9ca0     0x4  tasks.o [4]
  .bss               zero     0x2000'9ca4     0x4  tasks.o [4]
  .bss               zero     0x2000'9ca8     0x4  tasks.o [4]
  .bss               zero     0x2000'9cac     0x4  tasks.o [4]
  .bss               zero     0x2000'9cb0     0x4  tasks.o [4]
  .bss               zero     0x2000'9cb4     0x4  tasks.o [4]
  .bss               zero     0x2000'9cb8     0x4  tasks.o [4]
  .bss               zero     0x2000'9cbc     0x4  tasks.o [4]
  .bss               zero     0x2000'9cc0     0x4  tasks.o [4]
  .bss               zero     0x2000'9cc4     0x4  tasks.o [4]
  .bss               zero     0x2000'9cc8     0x4  tasks.o [4]
  .bss               zero     0x2000'9ccc     0x4  tasks.o [4]
  .bss               zero     0x2000'9cd0     0x4  tasks.o [4]
  .bss               zero     0x2000'9cd4     0x4  tasks.o [4]
  .bss               zero     0x2000'9cd8     0x4  timers.o [4]
  .bss               zero     0x2000'9cdc     0x4  timers.o [4]
  .bss               zero     0x2000'9ce0     0x4  timers.o [4]
  .bss               zero     0x2000'9ce4     0x4  timers.o [4]
  .bss               zero     0x2000'9ce8     0x4  timers.o [4]
  .bss               zero     0x2000'9cec     0x4  xfail_s.o [10]
  .bss               zero     0x2000'9cf0     0x2  userRtos.o [6]
  .bss               zero     0x2000'9cf2     0x1  port.o [4]
                            - 0x2000'9cf3  0x9c67

"P2", part 3 of 3:                         0x1000
  CSTACK                      0x2000'9cf8  0x1000  <Block>
    CSTACK           uninit   0x2000'9cf8  0x1000  <Block tail>
                            - 0x2000'acf8  0x1000

Unused ranges:

         From           To      Size
         ----           --      ----
   0x800'6eff   0x807'ffff  0x7'9101
  0x2000'0089  0x2000'008b       0x3
  0x2000'9cf4  0x2000'9cf7       0x4
  0x2000'acf8  0x2000'ffff    0x5308


*******************************************************************************
*** INIT TABLE
***

          Address      Size
          -------      ----
Zero (__iar_zero_init3)
    1 destination range, total size 0x9c67:
          0x2000'008c  0x9c67

Copy/packbits (__iar_packbits_init_single3)
    1 source range, total size 0x5d (67% of destination):
           0x800'6ea2    0x5d
    1 destination range, total size 0x89:
          0x2000'0000    0x89



*******************************************************************************
*** MODULE SUMMARY
***

    Module                        ro code  ro data  rw data
    ------                        -------  -------  -------
command line/config:
    -------------------------------------------------------
    Total:

D:\routine\stm32f1_software\software_All_example\compileFile\Obj\CMSIS_6603591812247902717.dir: [1]
    system_stm32f1xx.o                  2       26        4
    -------------------------------------------------------
    Total:                              2       26        4

D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir: [2]
    dma.o                              76
    freertos.o                        116      116        8
    gpio.o                            124
    i2c.o                             168                84
    main.o                            220       16
    spi.o                             204                88
    stm32f1xx_hal_msp.o                84
    stm32f1xx_hal_timebase_tim.o      148                72
    stm32f1xx_it.o                    116
    usart.o                           748               408
    -------------------------------------------------------
    Total:                          2'004      132      660

D:\routine\stm32f1_software\software_All_example\compileFile\Obj\EWARM_18443280873093131863.dir: [3]
    startup_stm32f103xe.o             532
    -------------------------------------------------------
    Total:                            532

D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir: [4]
    cmsis_os2.o                       440             1'756
    heap_4.o                          632        4   30'744
    list.o                            154
    port.o                            516        7        9
    portasm.o                         136
    queue.o                         1'904                64
    tasks.o                         2'644        8    1'280
    timers.o                        1'144       16      300
    -------------------------------------------------------
    Total:                          7'570       35   34'153

D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir: [5]
    stm32f1xx_hal.o                   112        3        9
    stm32f1xx_hal_cortex.o            268
    stm32f1xx_hal_dma.o             1'644
    stm32f1xx_hal_gpio.o              672
    stm32f1xx_hal_i2c.o               492
    stm32f1xx_hal_rcc.o             1'684       18
    stm32f1xx_hal_spi.o               196
    stm32f1xx_hal_tim.o               814
    stm32f1xx_hal_tim_ex.o              4
    stm32f1xx_hal_uart.o            1'352
    -------------------------------------------------------
    Total:                          7'238       21        9

D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir: [6]
    animal.o                          112      100
    dataSerialization.o                                  12
    debugApp.o                        396             3'608
    print.o                           400       88
    structAndFuc.o                    200       73       60
    userRtos.o                        652      116    1'578
    utils.o                           616       24
    -------------------------------------------------------
    Total:                          2'376      401    5'258

D:\routine\stm32f1_software\software_All_example\compileFile\Obj\light_fn_18089876452921840385.dir: [7]
    led.o                              68       28       12
    lsd.o                              68       28       12
    -------------------------------------------------------
    Total:                            136       56       24

D:\routine\stm32f1_software\software_All_example\compileFile\Obj\mid_1628371642073116930.dir: [8]
    -------------------------------------------------------
    Total:

D:\routine\stm32f1_software\software_All_example\compileFile\Obj\sfud_6715763213700909193.dir: [9]
    -------------------------------------------------------
    Total:

dl7M_tlf.a: [10]
    XShttio.o                           8        6        8
    abort.o                             6
    exit.o                              4
    low_level_init.o                    4
    vsnprint.o                         60
    xfail_s.o                          64                 4
    xlocale_c.o                         8       41       56
    xprintffull_nomb.o              3'786
    xsnprout.o                         26
    -------------------------------------------------------
    Total:                          3'966       47       68

m7M_tl.a: [11]
    DblAddSub.o                       590
    DblCmpGe.o                         52
    DblCmpLe.o                         52
    DblDiv.o                          582
    DblMul.o                          426
    DblToS32.o                         54
    DblToU32.o                         34
    S32ToDbl.o                         34
    U32ToDbl.o                         28
    frexp.o                           112
    ldexp.o                           314
    -------------------------------------------------------
    Total:                          2'278

rt7M_tl.a: [12]
    ABImemcpy.o                       166
    ABImemset.o                       102
    I64DivMod.o                       560
    I64DivZer.o                         2
    cexit.o                            10
    cmain.o                            30
    cstartup_M.o                       28
    data_init.o                        40
    memchr.o                           88
    memcmp.o                          104
    memset.o                           20
    packbits_init_single.o             58
    strchr.o                           22
    strcpy.o                           24
    strlen.o                           54
    strncpy.o                         112
    zero_init3.o                       56
    -------------------------------------------------------
    Total:                          1'476

shb_l.a: [13]
    exit.o                             20
    iarttio.o                          44
    -------------------------------------------------------
    Total:                             64

    Gaps                               21        2
    Linker created                              32    4'096
-----------------------------------------------------------
    Grand Total:                   27'663      752   44'272


*******************************************************************************
*** ENTRY LIST
***

Entry                       Address    Size  Type      Object
-----                       -------    ----  ----      ------
.iar.init_table$$Base    0x800'6b28           --   Gb  - Linker created -
.iar.init_table$$Limit   0x800'6b48           --   Gb  - Linker created -
?main                    0x800'6b49          Code  Gb  cmain.o [12]
ADC1_2_IRQHandler        0x800'6e09          Code  Wk  startup_stm32f103xe.o [3]
ADC3_IRQHandler          0x800'6e6d          Code  Wk  startup_stm32f103xe.o [3]
AHBPrescTable            0x800'6c80    0x10  Data  Gb  system_stm32f1xx.o [1]
APBPrescTable            0x800'6d84     0x8  Data  Gb  system_stm32f1xx.o [1]
AnimalTestTask           0x800'638d    0x30  Code  Gb  animal.o [6]
BusFault_Handler         0x800'6a13     0x2  Code  Gb  stm32f1xx_it.o [2]
CAN1_RX1_IRQHandler      0x800'6e15          Code  Wk  startup_stm32f103xe.o [3]
CAN1_SCE_IRQHandler      0x800'6e19          Code  Wk  startup_stm32f103xe.o [3]
CSTACK$$Base            0x2000'9cf8           --   Gb  - Linker created -
CSTACK$$Limit           0x2000'acf8           --   Gb  - Linker created -
Cat_eat                  0x800'6375    0x18  Code  Gb  animal.o [6]
DMA1_Channel1_IRQHandler
                         0x800'6df9          Code  Wk  startup_stm32f103xe.o [3]
DMA1_Channel2_IRQHandler
                         0x800'6dfd          Code  Wk  startup_stm32f103xe.o [3]
DMA1_Channel3_IRQHandler
                         0x800'6a19     0xa  Code  Gb  stm32f1xx_it.o [2]
DMA1_Channel4_IRQHandler
                         0x800'6e01          Code  Wk  startup_stm32f103xe.o [3]
DMA1_Channel5_IRQHandler
                         0x800'6a23     0xa  Code  Gb  stm32f1xx_it.o [2]
DMA1_Channel6_IRQHandler
                         0x800'6a2d     0xa  Code  Gb  stm32f1xx_it.o [2]
DMA1_Channel7_IRQHandler
                         0x800'6e05          Code  Wk  startup_stm32f103xe.o [3]
DMA2_Channel1_IRQHandler
                         0x800'6e91          Code  Wk  startup_stm32f103xe.o [3]
DMA2_Channel2_IRQHandler
                         0x800'6e95          Code  Wk  startup_stm32f103xe.o [3]
DMA2_Channel3_IRQHandler
                         0x800'6e99          Code  Wk  startup_stm32f103xe.o [3]
DMA2_Channel4_5_IRQHandler
                         0x800'6e9d          Code  Wk  startup_stm32f103xe.o [3]
DebugITSendBuf           0x800'4d2d    0x7a  Code  Gb  debugApp.o [6]
DebugMon_Handler         0x800'6a17     0x2  Code  Gb  stm32f1xx_it.o [2]
DebugUartCtrl           0x2000'9c54    0x10  Data  Gb  debugApp.o [6]
DebugUartEvent           0x800'4da7    0x88  Code  Gb  debugApp.o [6]
DebugUartInit            0x800'4cf1    0x3c  Code  Gb  debugApp.o [6]
Dog_eat                  0x800'6365    0x10  Code  Gb  animal.o [6]
EXTI0_IRQHandler         0x800'6de5          Code  Wk  startup_stm32f103xe.o [3]
EXTI15_10_IRQHandler     0x800'6e51          Code  Wk  startup_stm32f103xe.o [3]
EXTI1_IRQHandler         0x800'6de9          Code  Wk  startup_stm32f103xe.o [3]
EXTI2_IRQHandler         0x800'6ded          Code  Wk  startup_stm32f103xe.o [3]
EXTI3_IRQHandler         0x800'6df1          Code  Wk  startup_stm32f103xe.o [3]
EXTI4_IRQHandler         0x800'6df5          Code  Wk  startup_stm32f103xe.o [3]
EXTI9_5_IRQHandler       0x800'6e1d          Code  Wk  startup_stm32f103xe.o [3]
Error_Handler            0x800'4799     0x4  Code  Gb  main.o [2]
FLASH_IRQHandler         0x800'6ddd          Code  Wk  startup_stm32f103xe.o [3]
FSMC_IRQHandler          0x800'6e71          Code  Wk  startup_stm32f103xe.o [3]
HAL_DMA_Abort            0x800'5803    0x4e  Code  Gb  stm32f1xx_hal_dma.o [5]
HAL_DMA_Abort_IT         0x800'5851   0x1be  Code  Gb  stm32f1xx_hal_dma.o [5]
HAL_DMA_IRQHandler       0x800'5a55   0x358  Code  Gb  stm32f1xx_hal_dma.o [5]
HAL_DMA_Init             0x800'5779    0x8a  Code  Gb  stm32f1xx_hal_dma.o [5]
HAL_Delay                0x800'4ef1    0x28  Code  Wk  stm32f1xx_hal.o [5]
HAL_GPIO_Init            0x800'4f99   0x23c  Code  Gb  stm32f1xx_hal_gpio.o [5]
HAL_GPIO_WritePin        0x800'51d5    0x1a  Code  Gb  stm32f1xx_hal_gpio.o [5]
HAL_GetTick              0x800'41e9     0xc  Code  Wk  stm32f1xx_hal.o [5]
HAL_I2C_Init             0x800'5ea9   0x1d2  Code  Gb  stm32f1xx_hal_i2c.o [5]
HAL_I2C_MspInit          0x800'4c81    0x58  Code  Gb  i2c.o [2]
HAL_IncTick              0x800'4f19    0x18  Code  Wk  stm32f1xx_hal.o [5]
HAL_Init                 0x800'47a5    0x20  Code  Gb  stm32f1xx_hal.o [5]
HAL_InitTick             0x800'41f5    0x84  Code  Gb  stm32f1xx_hal_timebase_tim.o [2]
HAL_MspInit              0x800'4f31    0x54  Code  Gb  stm32f1xx_hal_msp.o [2]
HAL_NVIC_EnableIRQ       0x800'4371     0xe  Code  Gb  stm32f1xx_hal_cortex.o [5]
HAL_NVIC_SetPriority     0x800'4347    0x2a  Code  Gb  stm32f1xx_hal_cortex.o [5]
HAL_NVIC_SetPriorityGrouping
                         0x800'433b     0xc  Code  Gb  stm32f1xx_hal_cortex.o [5]
HAL_RCC_ClockConfig      0x800'3f55   0x14e  Code  Gb  stm32f1xx_hal_rcc.o [5]
HAL_RCC_GetClockConfig   0x800'4145    0x34  Code  Gb  stm32f1xx_hal_rcc.o [5]
HAL_RCC_GetHCLKFreq      0x800'4113     0x6  Code  Gb  stm32f1xx_hal_rcc.o [5]
HAL_RCC_GetPCLK1Freq     0x800'4119    0x16  Code  Gb  stm32f1xx_hal_rcc.o [5]
HAL_RCC_GetPCLK2Freq     0x800'412f    0x16  Code  Gb  stm32f1xx_hal_rcc.o [5]
HAL_RCC_GetSysClockFreq
                         0x800'40a3    0x70  Code  Gb  stm32f1xx_hal_rcc.o [5]
HAL_RCC_OscConfig        0x800'3b55   0x400  Code  Gb  stm32f1xx_hal_rcc.o [5]
HAL_SPI_Init             0x800'5de5    0xc4  Code  Gb  stm32f1xx_hal_spi.o [5]
HAL_SPI_MspInit          0x800'4bc3    0x72  Code  Gb  spi.o [2]
HAL_TIMEx_BreakCallback
                         0x800'479d     0x2  Code  Wk  stm32f1xx_hal_tim_ex.o [5]
HAL_TIMEx_CommutCallback
                         0x800'47a1     0x2  Code  Wk  stm32f1xx_hal_tim_ex.o [5]
HAL_TIM_Base_Init        0x800'4395    0x6e  Code  Gb  stm32f1xx_hal_tim.o [5]
HAL_TIM_Base_MspInit     0x800'46b9     0x2  Code  Wk  stm32f1xx_hal_tim.o [5]
HAL_TIM_Base_Start_IT    0x800'4403    0x86  Code  Gb  stm32f1xx_hal_tim.o [5]
HAL_TIM_IC_CaptureCallback
                         0x800'46bb     0x2  Code  Wk  stm32f1xx_hal_tim.o [5]
HAL_TIM_IRQHandler       0x800'449d   0x176  Code  Gb  stm32f1xx_hal_tim.o [5]
HAL_TIM_OC_DelayElapsedCallback
                         0x800'46bd     0x2  Code  Wk  stm32f1xx_hal_tim.o [5]
HAL_TIM_PWM_PulseFinishedCallback
                         0x800'46bf     0x2  Code  Wk  stm32f1xx_hal_tim.o [5]
HAL_TIM_PeriodElapsedCallback
                         0x800'477b    0x12  Code  Gb  main.o [2]
HAL_TIM_TriggerCallback
                         0x800'479f     0x2  Code  Wk  stm32f1xx_hal_tim.o [5]
HAL_UARTEx_RxEventCallback
                         0x800'60d3     0x2  Code  Wk  stm32f1xx_hal_uart.o [5]
HAL_UART_ErrorCallback   0x800'60d1     0x2  Code  Wk  stm32f1xx_hal_uart.o [5]
HAL_UART_IRQHandler      0x800'52af   0x232  Code  Gb  stm32f1xx_hal_uart.o [5]
HAL_UART_Init            0x800'5239    0x76  Code  Gb  stm32f1xx_hal_uart.o [5]
HAL_UART_MspInit         0x800'492d   0x20a  Code  Gb  usart.o [2]
HAL_UART_RxCpltCallback
                         0x800'60d7     0x2  Code  Wk  stm32f1xx_hal_uart.o [5]
HAL_UART_TxCpltCallback
                         0x800'60d5     0x2  Code  Wk  stm32f1xx_hal_uart.o [5]
HardFault_Handler        0x800'6a0f     0x2  Code  Gb  stm32f1xx_it.o [2]
I2C1_ER_IRQHandler       0x800'6e3d          Code  Wk  startup_stm32f103xe.o [3]
I2C1_EV_IRQHandler       0x800'6e39          Code  Wk  startup_stm32f103xe.o [3]
I2C2_ER_IRQHandler       0x800'6e45          Code  Wk  startup_stm32f103xe.o [3]
I2C2_EV_IRQHandler       0x800'6e41          Code  Wk  startup_stm32f103xe.o [3]
Idle_Stack              0x2000'92f4   0x200  Data  Lc  cmsis_os2.o [4]
Idle_TCB                0x2000'9794    0x6c  Data  Lc  cmsis_os2.o [4]
KernelState             0x2000'9c78     0x4  Data  Lc  cmsis_os2.o [4]
ListInit                 0x800'60f5    0x20  Code  Lc  userRtos.o [6]
ListItemInit             0x800'6115     0x6  Code  Lc  userRtos.o [6]
ListItemInsertEnd        0x800'611b    0x36  Code  Lc  userRtos.o [6]
ListItemInsertRemove     0x800'617d    0x20  Code  Lc  userRtos.o [6]
ListItemInsertValue      0x800'6151    0x2c  Code  Lc  userRtos.o [6]
ListTask                 0x800'6227   0x100  Code  Gb  userRtos.o [6]
ListTaskHandle          0x2000'9c80     0x4  Data  Gb  freertos.o [2]
ListTask_attributes      0x800'6b04    0x24  Data  Gb  freertos.o [2]
ListTestTask             0x800'619d    0x8a  Code  Lc  userRtos.o [6]
MX_DMA_Init              0x800'4845    0x4c  Code  Gb  dma.o [2]
MX_FREERTOS_Init         0x800'4e7d    0x22  Code  Gb  freertos.o [2]
MX_GPIO_Init             0x800'47c9    0x7c  Code  Gb  gpio.o [2]
MX_I2C1_Init             0x800'4c49    0x38  Code  Gb  i2c.o [2]
MX_SPI2_Init             0x800'4b7d    0x46  Code  Gb  spi.o [2]
MX_USART1_UART_Init      0x800'4891    0x34  Code  Gb  usart.o [2]
MX_USART2_UART_Init      0x800'48c5    0x34  Code  Gb  usart.o [2]
MX_USART3_UART_Init      0x800'48f9    0x34  Code  Gb  usart.o [2]
MemManage_Handler        0x800'6a11     0x2  Code  Gb  stm32f1xx_it.o [2]
NMI_Handler              0x800'6a0d     0x2  Code  Gb  stm32f1xx_it.o [2]
NVIC_EncodePriority      0x800'42fb    0x40  Code  Lc  stm32f1xx_hal_cortex.o [5]
PVD_IRQHandler           0x800'6dd1          Code  Wk  startup_stm32f103xe.o [3]
PendSV_Handler           0x800'335d          Code  Gb  portasm.o [4]
RCC_Delay                0x800'4179    0x26  Code  Lc  stm32f1xx_hal_rcc.o [5]
RCC_IRQHandler           0x800'6de1          Code  Wk  startup_stm32f103xe.o [3]
RTC_Alarm_IRQHandler     0x800'6e55          Code  Wk  startup_stm32f103xe.o [3]
RTC_IRQHandler           0x800'6dd9          Code  Wk  startup_stm32f103xe.o [3]
Region$$Table$$Base      0x800'6b28           --   Gb  - Linker created -
Region$$Table$$Limit     0x800'6b48           --   Gb  - Linker created -
Reset_Handler            0x800'6cb1          Code  Wk  startup_stm32f103xe.o [3]
SDIO_IRQHandler          0x800'6e75          Code  Wk  startup_stm32f103xe.o [3]
SPI1_IRQHandler          0x800'6e49          Code  Wk  startup_stm32f103xe.o [3]
SPI2_IRQHandler          0x800'6e4d          Code  Wk  startup_stm32f103xe.o [3]
SPI3_IRQHandler          0x800'6e7d          Code  Wk  startup_stm32f103xe.o [3]
SVC_Handler              0x800'33a3          Code  Gb  portasm.o [4]
StartDefaultTask         0x800'4ea1    0x36  Code  Gb  freertos.o [2]
SysTick_Handler          0x800'2c61    0x28  Code  Gb  port.o [4]
SystemClock_Config       0x800'470d    0x6e  Code  Gb  main.o [2]
SystemCoreClock         0x2000'0044     0x4  Data  Gb  system_stm32f1xx.o [1]
SystemInit               0x800'6ea1     0x2  Code  Gb  system_stm32f1xx.o [1]
TAMPER_IRQHandler        0x800'6dd5          Code  Wk  startup_stm32f103xe.o [3]
TIM1_BRK_IRQHandler      0x800'6e21          Code  Wk  startup_stm32f103xe.o [3]
TIM1_CC_IRQHandler       0x800'6e29          Code  Wk  startup_stm32f103xe.o [3]
TIM1_TRG_COM_IRQHandler
                         0x800'6e25          Code  Wk  startup_stm32f103xe.o [3]
TIM1_UP_IRQHandler       0x800'6a37     0xa  Code  Gb  stm32f1xx_it.o [2]
TIM2_IRQHandler          0x800'6e2d          Code  Wk  startup_stm32f103xe.o [3]
TIM3_IRQHandler          0x800'6e31          Code  Wk  startup_stm32f103xe.o [3]
TIM4_IRQHandler          0x800'6e35          Code  Wk  startup_stm32f103xe.o [3]
TIM5_IRQHandler          0x800'6e79          Code  Wk  startup_stm32f103xe.o [3]
TIM6_IRQHandler          0x800'6e89          Code  Wk  startup_stm32f103xe.o [3]
TIM7_IRQHandler          0x800'6e8d          Code  Wk  startup_stm32f103xe.o [3]
TIM8_BRK_IRQHandler      0x800'6e5d          Code  Wk  startup_stm32f103xe.o [3]
TIM8_CC_IRQHandler       0x800'6e69          Code  Wk  startup_stm32f103xe.o [3]
TIM8_TRG_COM_IRQHandler
                         0x800'6e65          Code  Wk  startup_stm32f103xe.o [3]
TIM8_UP_IRQHandler       0x800'6e61          Code  Wk  startup_stm32f103xe.o [3]
TIM_Base_SetConfig       0x800'4613    0x90  Code  Gb  stm32f1xx_hal_tim.o [5]
Timer_Stack             0x2000'8af4   0x400  Data  Lc  cmsis_os2.o [4]
Timer_TCB               0x2000'9800    0x6c  Data  Lc  cmsis_os2.o [4]
UART4_IRQHandler         0x800'6e81          Code  Wk  startup_stm32f103xe.o [3]
UART5_IRQHandler         0x800'6e85          Code  Wk  startup_stm32f103xe.o [3]
UART_DMAAbortOnError     0x800'5519    0x16  Code  Lc  stm32f1xx_hal_uart.o [5]
UART_EndRxTransfer       0x800'54e1    0x36  Code  Lc  stm32f1xx_hal_uart.o [5]
UART_EndTransmit_IT      0x800'5597    0x20  Code  Lc  stm32f1xx_hal_uart.o [5]
UART_Receive_IT          0x800'55b7    0xe2  Code  Lc  stm32f1xx_hal_uart.o [5]
UART_SetConfig           0x800'5699    0xd8  Code  Lc  stm32f1xx_hal_uart.o [5]
UART_Transmit_IT         0x800'5531    0x66  Code  Lc  stm32f1xx_hal_uart.o [5]
USART1_IRQHandler        0x800'6a41     0xa  Code  Gb  stm32f1xx_it.o [2]
USART2_IRQHandler        0x800'6a4b     0xe  Code  Gb  stm32f1xx_it.o [2]
USART3_IRQHandler        0x800'6a59     0xa  Code  Gb  stm32f1xx_it.o [2]
USBWakeUp_IRQHandler     0x800'6e59          Code  Wk  startup_stm32f103xe.o [3]
USB_HP_CAN1_TX_IRQHandler
                         0x800'6e0d          Code  Wk  startup_stm32f103xe.o [3]
USB_LP_CAN1_RX0_IRQHandler
                         0x800'6e11          Code  Wk  startup_stm32f103xe.o [3]
UsageFault_Handler       0x800'6a15     0x2  Code  Gb  stm32f1xx_it.o [2]
WWDG_IRQHandler          0x800'6dcd          Code  Wk  startup_stm32f103xe.o [3]
_LC                      0x800'0131     0x6  Code  Lc  xprintffull_nomb.o [10]
_LitobFullNoMb           0x800'0e19   0x114  Code  Lc  xprintffull_nomb.o [10]
_Locale_lconv           0x2000'0048    0x38  Data  Lc  xlocale_c.o [10]
_PrintfFullNoMb          0x800'0137   0xcb2  Code  Gb  xprintffull_nomb.o [10]
_PutcharsFullNoMb        0x800'0f85    0x2e  Code  Lc  xprintffull_nomb.o [10]
_SNProut                 0x800'104b    0x1a  Code  Gb  xsnprout.o [10]
__NVIC_EnableIRQ         0x800'42b1    0x1e  Code  Lc  stm32f1xx_hal_cortex.o [5]
__NVIC_GetPriorityGrouping
                         0x800'42a7     0xa  Code  Lc  stm32f1xx_hal_cortex.o [5]
__NVIC_SetPriority       0x800'42cf    0x2c  Code  Lc  stm32f1xx_hal_cortex.o [5]
__NVIC_SetPriorityGrouping
                         0x800'4289    0x1e  Code  Lc  stm32f1xx_hal_cortex.o [5]
__aeabi_cdcmple          0x800'124d          Code  Gb  DblCmpLe.o [11]
__aeabi_cdrcmple         0x800'1281          Code  Gb  DblCmpGe.o [11]
__aeabi_d2iz             0x800'13f1          Code  Gb  DblToS32.o [11]
__aeabi_d2uiz            0x800'18e5          Code  Gb  DblToU32.o [11]
__aeabi_ddiv             0x800'169d          Code  Gb  DblDiv.o [11]
__aeabi_dmul             0x800'1925          Code  Gb  DblMul.o [11]
__aeabi_dsub             0x800'15b9          Code  Gb  DblAddSub.o [11]
__aeabi_i2d              0x800'1429          Code  Gb  S32ToDbl.o [11]
__aeabi_ldiv0            0x800'1d11          Code  Gb  I64DivZer.o [12]
__aeabi_memcpy           0x800'1135          Code  Gb  ABImemcpy.o [12]
__aeabi_memcpy4          0x800'1155          Code  Gb  ABImemcpy.o [12]
__aeabi_memcpy8          0x800'1155          Code  Gb  ABImemcpy.o [12]
__aeabi_memset           0x800'2a29          Code  Gb  ABImemset.o [12]
__aeabi_ui2d             0x800'1909          Code  Gb  U32ToDbl.o [11]
__aeabi_uldivmod         0x800'1ad9          Code  Gb  I64DivMod.o [12]
__cmain                  0x800'6b49          Code  Gb  cmain.o [12]
__exit                   0x800'1d15    0x14  Code  Gb  exit.o [13]
__iar_Fail_s             0x800'1065    0x1c  Code  Gb  xfail_s.o [10]
__iar_Memchr             0x800'10dd          Code  Gb  memchr.o [12]
__iar_Memset             0x800'2a29          Code  Gb  ABImemset.o [12]
__iar_Memset_word        0x800'2a31          Code  Gb  ABImemset.o [12]
__iar_Strchr             0x800'1035          Code  Gb  strchr.o [12]
__iar_close_ttio         0x800'1d29    0x2c  Code  Gb  iarttio.o [13]
__iar_data_init3         0x800'6ab9    0x28  Code  Gb  data_init.o [12]
__iar_frexp              0x800'11e9          Code  Gb  frexp.o [11]
__iar_frexp64            0x800'11dd          Code  Gb  frexp.o [11]
__iar_frexpl             0x800'11e9          Code  Gb  frexp.o [11]
__iar_ldexp64            0x800'12b5          Code  Gb  ldexp.o [11]
__iar_lookup_ttioh       0x800'1d55     0x8  Code  Gb  XShttio.o [10]
__iar_packbits_init_single3
                         0x800'0ffb    0x3a  Code  Gb  packbits_init_single.o [12]
__iar_program_start      0x800'6bb5          Code  Gb  cstartup_M.o [12]
__iar_scalbln64          0x800'12b5          Code  Gb  ldexp.o [11]
__iar_scalbn64           0x800'12b5          Code  Gb  ldexp.o [11]
__iar_ttio_handles      0x2000'0080     0x8  Data  Lc  XShttio.o [10]
__iar_zero_init3         0x800'6a81    0x38  Code  Gb  zero_init3.o [12]
__low_level_init         0x800'6b67     0x4  Code  Gb  low_level_init.o [10]
__vector_table           0x800'0000          Data  Gb  startup_stm32f103xe.o [3]
_call_main               0x800'6b55          Code  Gb  cmain.o [12]
_exit                    0x800'6b71          Code  Gb  cexit.o [12]
abort                    0x800'1d09     0x6  Code  Gb  abort.o [10]
color_printer            0x800'6dc8     0x4  Data  Lc  print.o [6]
color_printer_SetColor   0x800'648b    0x12  Code  Gb  print.o [6]
color_printer_delete     0x800'6479    0x12  Code  Gb  print.o [6]
color_printer_new        0x800'6435    0x44  Code  Lc  print.o [6]
color_printer_print      0x800'6411    0x24  Code  Lc  print.o [6]
dataStruct              0x2000'9c64     0xc  Data  Lc  dataSerialization.o [6]
debugRecvRingBuf        0x2000'94f4   0x200  Data  Gb  debugApp.o [6]
debugSendRingBuf        0x2000'8ef4   0x400  Data  Gb  debugApp.o [6]
debug_buffer            0x2000'788c   0x808  Data  Gb  debugApp.o [6]
defaultTaskHandle       0x2000'9c7c     0x4  Data  Gb  freertos.o [2]
defaultTask_attributes   0x800'6ae0    0x24  Data  Gb  freertos.o [2]
delayList               0x2000'9c2c    0x14  Data  Lc  userRtos.o [6]
exit                     0x800'6b6b     0x4  Code  Gb  exit.o [10]
frexp                    0x800'11dd          Code  Gb  frexp.o [11]
frexpl                   0x800'11dd          Code  Gb  frexp.o [11]
hdma_usart1_rx          0x2000'9a7c    0x44  Data  Gb  usart.o [2]
hdma_usart2_rx          0x2000'9ac0    0x44  Data  Gb  usart.o [2]
hdma_usart3_rx          0x2000'9b04    0x44  Data  Gb  usart.o [2]
hi2c1                   0x2000'98c4    0x54  Data  Gb  i2c.o [2]
hspi2                   0x2000'986c    0x58  Data  Gb  spi.o [2]
htim1                   0x2000'9968    0x48  Data  Gb  stm32f1xx_hal_timebase_tim.o [2]
huart1                  0x2000'99b0    0x44  Data  Gb  usart.o [2]
huart2                  0x2000'99f4    0x44  Data  Gb  usart.o [2]
huart3                  0x2000'9a38    0x44  Data  Gb  usart.o [2]
ldexp                    0x800'12b5          Code  Gb  ldexp.o [11]
ldexpl                   0x800'12b5          Code  Gb  ldexp.o [11]
led_get_state            0x800'65cd     0x4  Code  Gb  led.o [7]
led_init                 0x800'65d1     0xe  Code  Gb  led.o [7]
led_interface           0x2000'0000     0xc  Data  Lc  led.o [7]
led_off                  0x800'65bb    0x12  Code  Gb  led.o [7]
led_on                   0x800'65a9    0x12  Code  Gb  led.o [7]
light_off                0x800'60e7     0xe  Code  Lc  userRtos.o [6]
light_on                 0x800'60d9     0xe  Code  Lc  userRtos.o [6]
listStaticMaxNum        0x2000'9cf0     0x2  Data  Lc  userRtos.o [6]
localeconv               0x800'1ad1     0x4  Code  Gb  xlocale_c.o [10]
lsd_get_state            0x800'6589     0x4  Code  Gb  lsd.o [7]
lsd_init                 0x800'658d     0xe  Code  Gb  lsd.o [7]
lsd_interface           0x2000'000c     0xc  Data  Lc  lsd.o [7]
lsd_off                  0x800'6577    0x12  Code  Gb  lsd.o [7]
lsd_on                   0x800'6565    0x12  Code  Gb  lsd.o [7]
main                     0x800'46c1    0x4c  Code  Gb  main.o [2]
memchr                   0x800'10dd          Code  Gb  memchr.o [12]
memcmp                   0x800'6855          Code  Gb  memcmp.o [12]
memset                   0x800'4f85    0x14  Code  Gb  memset.o [12]
osKernelInitialize       0x800'2cf9    0x3c  Code  Gb  cmsis_os2.o [4]
osKernelStart            0x800'2d35    0x42  Code  Gb  cmsis_os2.o [4]
osThreadNew              0x800'2d77   0x106  Code  Gb  cmsis_os2.o [4]
pcInterruptPriorityRegisters
                         0x800'6dc0     0x4  Data  Lc  port.o [4]
plain_printer_delete     0x800'6405     0xc  Code  Lc  print.o [6]
plain_printer_new        0x800'63e7    0x1e  Code  Gb  print.o [6]
plain_printer_print      0x800'63d5    0x12  Code  Lc  print.o [6]
printer_interface        0x800'6dc4     0x4  Data  Lc  print.o [6]
prvAddCurrentTaskToDelayedList
                         0x800'2711    0x6c  Code  Lc  tasks.o [4]
prvAddNewTaskToReadyList
                         0x800'1f47    0x9e  Code  Lc  tasks.o [4]
prvCheckForValidListAndQueue
                         0x800'32ad    0x4c  Code  Lc  timers.o [4]
prvCheckForValidListAndQueue{1}{2}{3}{4}::ucStaticTimerQueueStorage
                        0x2000'96f4    0xa0  Data  Lc  timers.o [4]
prvCheckForValidListAndQueue{1}{2}{3}{4}::xStaticTimerQueue
                        0x2000'9918    0x50  Data  Lc  timers.o [4]
prvCheckTasksWaitingTermination
                         0x800'258f    0x3e  Code  Lc  tasks.o [4]
prvCopyDataFromQueue     0x800'3a01    0x36  Code  Lc  queue.o [4]
prvCopyDataToQueue       0x800'3965    0x9c  Code  Lc  queue.o [4]
prvDeleteTCB             0x800'25d1    0x44  Code  Lc  tasks.o [4]
prvGetNextExpireTime     0x800'308b    0x2a  Code  Lc  timers.o [4]
prvHeapInit              0x800'2947    0x66  Code  Lc  heap_4.o [4]
prvIdleTask              0x800'2511    0x24  Code  Lc  tasks.o [4]
prvInitialiseNewQueue    0x800'3539    0x36  Code  Lc  queue.o [4]
prvInitialiseNewTask     0x800'1e67    0xe0  Code  Lc  tasks.o [4]
prvInitialiseTaskLists   0x800'2539    0x56  Code  Lc  tasks.o [4]
prvInsertBlockIntoFreeList
                         0x800'29ad    0x5e  Code  Lc  heap_4.o [4]
prvInsertTimerInActiveList
                         0x800'30dd    0x50  Code  Lc  timers.o [4]
prvIsQueueEmpty          0x800'3aa5    0x1c  Code  Lc  queue.o [4]
prvIsQueueFull           0x800'3ac1    0x1e  Code  Lc  queue.o [4]
prvProcessExpiredTimer   0x800'2f9f    0x5c  Code  Lc  timers.o [4]
prvProcessReceivedCommands
                         0x800'312d    0xf4  Code  Lc  timers.o [4]
prvProcessTimerOrBlockTask
                         0x800'3017    0x74  Code  Lc  timers.o [4]
prvResetNextTaskUnblockTime
                         0x800'2615    0x26  Code  Lc  tasks.o [4]
prvSampleTimeNow         0x800'30b5    0x28  Code  Lc  timers.o [4]
prvSampleTimeNow::xLastTime
                        0x2000'9ce8     0x4  Data  Lc  timers.o [4]
prvSwitchTimerLists      0x800'3221    0x8c  Code  Lc  timers.o [4]
prvTaskExitError         0x800'2b49    0x2a  Code  Lc  port.o [4]
prvTimerTask             0x800'2ffd    0x1a  Code  Lc  timers.o [4]
prvUnlockQueue           0x800'3a37    0x6e  Code  Lc  queue.o [4]
pvPortMalloc             0x800'27b1   0x122  Code  Gb  heap_4.o [4]
pxCurrentTCB            0x2000'9c9c     0x4  Data  Gb  tasks.o [4]
pxCurrentTimerList      0x2000'9cd8     0x4  Data  Lc  timers.o [4]
pxDelayedTaskList       0x2000'9ca0     0x4  Data  Lc  tasks.o [4]
pxEnd                   0x2000'9c84     0x4  Data  Lc  heap_4.o [4]
pxOverflowDelayedTaskList
                        0x2000'9ca4     0x4  Data  Lc  tasks.o [4]
pxOverflowTimerList     0x2000'9cdc     0x4  Data  Lc  timers.o [4]
pxPortInitialiseStack    0x800'2b29    0x20  Code  Gb  port.o [4]
pxReadyTasksLists       0x2000'8694   0x460  Data  Lc  tasks.o [4]
readyList               0x2000'9c40    0x14  Data  Lc  userRtos.o [6]
scalbln                  0x800'12b5          Code  Gb  ldexp.o [11]
scalblnl                 0x800'12b5          Code  Gb  ldexp.o [11]
scalbn                   0x800'12b5          Code  Gb  ldexp.o [11]
scalbnl                  0x800'12b5          Code  Gb  ldexp.o [11]
scale                    0x800'0f2d    0x46  Code  Lc  xprintffull_nomb.o [10]
sec_hand                0x2000'9cec     0x4  Data  Lc  xfail_s.o [10]
strchr                   0x800'1035          Code  Gb  strchr.o [12]
strcpy                   0x800'69f5          Code  Gb  strcpy.o [12]
strlen                   0x800'10a5          Code  Gb  strlen.o [12]
strncpy                  0x800'68bd          Code  Gb  strncpy.o [12]
stuPrivate              0x2000'9b88    0x18  Data  Lc  structAndFuc.o [6]
student_createAndArgs    0x800'6987    0x42  Code  Lc  structAndFuc.o [6]
student_createDefault    0x800'6977    0x10  Code  Lc  structAndFuc.o [6]
student_delete           0x800'692d    0x12  Code  Lc  structAndFuc.o [6]
student_f               0x2000'0020    0x24  Data  Gb  structAndFuc.o [6]
student_getAge           0x800'6953     0x6  Code  Lc  structAndFuc.o [6]
student_getName          0x800'695f     0x6  Code  Lc  structAndFuc.o [6]
student_print            0x800'693f    0x14  Code  Lc  structAndFuc.o [6]
student_setAge           0x800'6959     0x6  Code  Lc  structAndFuc.o [6]
student_setName          0x800'6965    0x12  Code  Lc  structAndFuc.o [6]
test_print               0x800'649d    0x8c  Code  Gb  print.o [6]
time_to_timestamp        0x800'65ed    0xe4  Code  Gb  utils.o [6]
time_to_timestamp::days_in_month
                         0x800'6d14     0xc  Data  Lc  utils.o [6]
timestamp_to_time        0x800'66d1   0x16a  Code  Gb  utils.o [6]
timestamp_to_time::days_in_month
                         0x800'6d20     0xc  Data  Lc  utils.o [6]
ucHeap                  0x2000'008c  0x7800  Data  Lc  heap_4.o [4]
ucMaxSysCallPriority    0x2000'9cf2     0x1  Data  Lc  port.o [4]
ulMaxPRIGROUPValue      0x2000'9c94     0x4  Data  Lc  port.o [4]
userContainerBuf        0x2000'8094   0x600  Data  Gb  userRtos.o [6]
user_printf              0x800'4e2f    0x30  Code  Gb  debugApp.o [6]
uwTick                  0x2000'9c98     0x4  Data  Gb  stm32f1xx_hal.o [5]
uwTickFreq              0x2000'0088     0x1  Data  Gb  stm32f1xx_hal.o [5]
uwTickPrio              0x2000'001c     0x4  Data  Gb  stm32f1xx_hal.o [5]
uxCriticalNesting       0x2000'0018     0x4  Data  Lc  port.o [4]
uxCurrentNumberOfTasks  0x2000'9cac     0x4  Data  Lc  tasks.o [4]
uxDeletedTasksWaitingCleanUp
                        0x2000'9ca8     0x4  Data  Lc  tasks.o [4]
uxListRemove             0x800'2b01    0x28  Code  Gb  list.o [4]
uxPendedTicks           0x2000'9cbc     0x4  Data  Lc  tasks.o [4]
uxSchedulerSuspended    0x2000'9cd4     0x4  Data  Lc  tasks.o [4]
uxTaskNumber            0x2000'9cc8     0x4  Data  Lc  tasks.o [4]
uxTopReadyPriority      0x2000'9cb4     0x4  Data  Lc  tasks.o [4]
vApplicationGetIdleTaskMemory
                         0x800'2e81     0xe  Code  Gb  cmsis_os2.o [4]
vApplicationGetTimerTaskMemory
                         0x800'2e8f    0x10  Code  Gb  cmsis_os2.o [4]
vListInitialise          0x800'2a8f    0x1e  Code  Gb  list.o [4]
vListInitialiseItem      0x800'2aad     0x6  Code  Gb  list.o [4]
vListInsert              0x800'2acb    0x36  Code  Gb  list.o [4]
vListInsertEnd           0x800'2ab3    0x18  Code  Gb  list.o [4]
vPortEnterCritical       0x800'2c01    0x34  Code  Gb  port.o [4]
vPortExitCritical        0x800'2c35    0x2c  Code  Gb  port.o [4]
vPortFree                0x800'28d3    0x74  Code  Gb  heap_4.o [4]
vPortSetupTimerInterrupt
                         0x800'3329    0x34  Code  Wk  port.o [4]
vPortStartFirstTask      0x800'33c3          Code  Gb  portasm.o [4]
vPortValidateInterruptPriority
                         0x800'2c89    0x4e  Code  Gb  port.o [4]
vQueueAddToRegistry      0x800'3adf    0x26  Code  Gb  queue.o [4]
vQueueWaitForMessageRestricted
                         0x800'3b09    0x4c  Code  Gb  queue.o [4]
vTaskDelay               0x800'1fe5    0x4e  Code  Gb  tasks.o [4]
vTaskInternalSetTimeOutState
                         0x800'245d    0x12  Code  Gb  tasks.o [4]
vTaskMissedYield         0x800'2503     0xa  Code  Gb  tasks.o [4]
vTaskPlaceOnEventList    0x800'2345    0x34  Code  Gb  tasks.o [4]
vTaskPlaceOnEventListRestricted
                         0x800'2385    0x40  Code  Gb  tasks.o [4]
vTaskStartScheduler      0x800'2033    0x9a  Code  Gb  tasks.o [4]
vTaskSuspendAll          0x800'20cd     0xc  Code  Gb  tasks.o [4]
vTaskSwitchContext       0x800'22d3    0x72  Code  Gb  tasks.o [4]
vsnprintf                0x800'6095    0x3c  Code  Gb  vsnprint.o [10]
xActiveTimerList1       0x2000'9c04    0x14  Data  Lc  timers.o [4]
xActiveTimerList2       0x2000'9c18    0x14  Data  Lc  timers.o [4]
xBlockAllocatedBit      0x2000'9c90     0x4  Data  Lc  heap_4.o [4]
xDelayedTaskList1       0x2000'9ba0    0x14  Data  Lc  tasks.o [4]
xDelayedTaskList2       0x2000'9bb4    0x14  Data  Lc  tasks.o [4]
xFreeBytesRemaining     0x2000'9c88     0x4  Data  Lc  heap_4.o [4]
xHeapStructSize          0x800'6dbc     0x4  Data  Lc  heap_4.o [4]
xIdleTaskHandle         0x2000'9cd0     0x4  Data  Lc  tasks.o [4]
xMinimumEverFreeBytesRemaining
                        0x2000'9c8c     0x4  Data  Lc  heap_4.o [4]
xNextTaskUnblockTime    0x2000'9ccc     0x4  Data  Lc  tasks.o [4]
xNumOfOverflows         0x2000'9cc4     0x4  Data  Lc  tasks.o [4]
xPendingReadyList       0x2000'9bc8    0x14  Data  Lc  tasks.o [4]
xPortStartScheduler      0x800'2b73    0x8e  Code  Gb  port.o [4]
xQueueGenericCreateStatic
                         0x800'3479    0xc0  Code  Gb  queue.o [4]
xQueueGenericReset       0x800'33e5    0x94  Code  Gb  queue.o [4]
xQueueGenericSend        0x800'356f   0x1a6  Code  Gb  queue.o [4]
xQueueGenericSendFromISR
                         0x800'3715    0xe0  Code  Gb  queue.o [4]
xQueueReceive            0x800'37f5   0x16a  Code  Gb  queue.o [4]
xQueueRegistry          0x2000'9b48    0x40  Data  Gb  queue.o [4]
xSchedulerRunning       0x2000'9cb8     0x4  Data  Lc  tasks.o [4]
xStart                  0x2000'9c70     0x8  Data  Lc  heap_4.o [4]
xSuspendedTaskList      0x2000'9bf0    0x14  Data  Lc  tasks.o [4]
xTaskCheckForTimeOut     0x800'2475    0x8e  Code  Gb  tasks.o [4]
xTaskCreate              0x800'1df5    0x72  Code  Gb  tasks.o [4]
xTaskCreateStatic        0x800'1d5d    0x98  Code  Gb  tasks.o [4]
xTaskGetSchedulerState   0x800'263b    0x20  Code  Gb  tasks.o [4]
xTaskGetTickCount        0x800'21c1     0x8  Code  Gb  tasks.o [4]
xTaskIncrementTick       0x800'21c9   0x10a  Code  Gb  tasks.o [4]
xTaskPriorityDisinherit
                         0x800'265b    0x8e  Code  Gb  tasks.o [4]
xTaskRemoveFromEventList
                         0x800'23d1    0x82  Code  Gb  tasks.o [4]
xTaskResumeAll           0x800'20d9    0xe8  Code  Gb  tasks.o [4]
xTasksWaitingTermination
                        0x2000'9bdc    0x14  Data  Lc  tasks.o [4]
xTickCount              0x2000'9cb0     0x4  Data  Lc  tasks.o [4]
xTimerCreateTimerTask    0x800'2eb1    0x6c  Code  Gb  timers.o [4]
xTimerGenericCommand     0x800'2f1d    0x82  Code  Gb  timers.o [4]
xTimerQueue             0x2000'9ce0     0x4  Data  Lc  timers.o [4]
xTimerTaskHandle        0x2000'9ce4     0x4  Data  Lc  timers.o [4]
xYieldPending           0x2000'9cc0     0x4  Data  Lc  tasks.o [4]


[1] = D:\routine\stm32f1_software\software_All_example\compileFile\Obj\CMSIS_6603591812247902717.dir
[2] = D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir
[3] = D:\routine\stm32f1_software\software_All_example\compileFile\Obj\EWARM_18443280873093131863.dir
[4] = D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir
[5] = D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir
[6] = D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir
[7] = D:\routine\stm32f1_software\software_All_example\compileFile\Obj\light_fn_18089876452921840385.dir
[8] = D:\routine\stm32f1_software\software_All_example\compileFile\Obj\mid_1628371642073116930.dir
[9] = D:\routine\stm32f1_software\software_All_example\compileFile\Obj\sfud_6715763213700909193.dir
[10] = dl7M_tlf.a
[11] = m7M_tl.a
[12] = rt7M_tl.a
[13] = shb_l.a

  27'663 bytes of readonly  code memory
     752 bytes of readonly  data memory
  44'272 bytes of readwrite data memory

Errors: none
Warnings: none
