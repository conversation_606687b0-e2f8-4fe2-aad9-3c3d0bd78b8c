###############################################################################
#
# IAR ELF Linker V9.40.1.364/W64 for ARM                  16/Aug/2025  18:30:12
# Copyright 2007-2023 IAR Systems AB.
#
#    Output file  =
#        D:\routine\stm32f1_software\software_All_example\compileFile\Exe\TEST_bootloader.out
#    Map file     =
#        D:\routine\stm32f1_software\software_All_example\compileFile\List\TEST_bootloader.map
#    Command line =
#        -f
#        D:\routine\stm32f1_software\software_All_example\compileFile\Exe\TEST_bootloader.out.rsp
#        (D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\animal.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\mid_1628371642073116930.dir\backUartMid.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\cmsis_os2.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\croutine.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\dataSerialization.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\debugApp.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\mid_1628371642073116930.dir\debugUartMid.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir\dma.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\event_groups.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir\freertos.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir\gpio.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\heap_4.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\light_fn_18089876452921840385.dir\led.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\light_fn_18089876452921840385.dir\light.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\list.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\light_fn_18089876452921840385.dir\lsd.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir\main.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\port.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\portasm.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\print.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\queue.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\ringbuffer.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\sfud_6715763213700909193.dir\sfud.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\sfud_6715763213700909193.dir\sfud_port.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\sfud_6715763213700909193.dir\sfud_sfdp.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir\spi.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\spi_nor_time_series.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\EWARM_18443280873093131863.dir\startup_stm32f103xe.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir\stm32f1xx_hal_msp.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir\stm32f1xx_it.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\stream_buffer.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\structAndFuc.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\CMSIS_6603591812247902717.dir\system_stm32f1xx.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\sysTickApp.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\tasks.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir\timers.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\uplinkEsp8266App.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\mid_1628371642073116930.dir\uplinkUartMid.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir\usart.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\userRtos.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\utils.o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir\W25Q128_flash.o
#        --no_out_extension -o
#        D:\routine\stm32f1_software\software_All_example\compileFile\Exe\TEST_bootloader.out
#        --redirect _Printf=_PrintfFullNoMb --redirect _Scanf=_ScanfFullNoMb
#        --map
#        D:\routine\stm32f1_software\software_All_example\compileFile\List\TEST_bootloader.map
#        --config
#        D:\routine\stm32f1_software\software_All_example\TEST_userSoftware\EWARM/stm32f103xe_flash.icf
#        --semihosting --entry __iar_program_start --vfe --text_out locale
#        --cpu=Cortex-M3 --fpu=None) --dependencies=n
#        D:\routine\stm32f1_software\software_All_example\compileFile\Exe\TEST_bootloader.out.iar_deps
#
###############################################################################

*******************************************************************************
*** RUNTIME MODEL ATTRIBUTES
***

CppFlavor       = *
__CPP_Runtime   = 1
__SystemLibrary = DLib
__dlib_version  = 6


*******************************************************************************
*** HEAP SELECTION
***

The basic heap was selected because --advanced_heap
was not specified and the application did not appear to
be primarily optimized for speed.


*******************************************************************************
*** PLACEMENT SUMMARY
***

"A0":  place at address 0x800'0000 { ro section .intvec };
"P1":  place in [from 0x800'0000 to 0x807'ffff] { ro };
define block CSTACK with size = 4K, alignment = 8 { };
define block HEAP with size = 512, alignment = 8 { };
"P2":  place in [from 0x2000'0000 to 0x2000'ffff] {
          rw, block CSTACK, block HEAP };
initialize by copy { rw };

  Section            Kind         Address    Size  Object
  -------            ----         -------    ----  ------
"A0":                                       0x130
  .intvec            ro code   0x800'0000   0x130  startup_stm32f103xe.o [3]
                             - 0x800'0130   0x130

"P1":                                      0x7dda
  .text              ro code   0x800'0130   0xeca  xprintffull_nomb.o [10]
  .text              ro code   0x800'0ffa    0x3a  packbits_init_single.o [12]
  .text              ro code   0x800'1034    0x16  strchr.o [12]
  .text              ro code   0x800'104a    0x1a  xsnprout.o [10]
  .text              ro code   0x800'1064    0x40  xfail_s.o [10]
  .text              ro code   0x800'10a4    0x36  strlen.o [12]
  .rodata            const     0x800'10da     0x2  stm32f1xx_hal_rcc.o [5]
  .text              ro code   0x800'10dc    0x58  memchr.o [12]
  .text              ro code   0x800'1134    0xa6  ABImemcpy.o [12]
  .rodata            const     0x800'11da     0x2  xlocale_c.o [10]
  .text              ro code   0x800'11dc    0x70  frexp.o [11]
  .text              ro code   0x800'124c    0x34  DblCmpLe.o [11]
  .text              ro code   0x800'1280    0x34  DblCmpGe.o [11]
  .text              ro code   0x800'12b4   0x13a  ldexp.o [11]
  .text              ro code   0x800'13ee     0x2  stm32f1xx_hal_uart.o [5]
  .text              ro code   0x800'13f0    0x36  DblToS32.o [11]
  .text              ro code   0x800'1426     0x2  stm32f1xx_hal_uart.o [5]
  .text              ro code   0x800'1428    0x22  S32ToDbl.o [11]
  .text              ro code   0x800'144a     0x2  stm32f1xx_hal_uart.o [5]
  .text              ro code   0x800'144c   0x24e  DblAddSub.o [11]
  .text              ro code   0x800'169a     0x2  stm32f1xx_hal_uart.o [5]
  .text              ro code   0x800'169c   0x246  DblDiv.o [11]
  .text              ro code   0x800'18e2     0x2  stm32f1xx_hal_uart.o [5]
  .text              ro code   0x800'18e4    0x22  DblToU32.o [11]
  .text              ro code   0x800'1906     0x2  stm32f1xx_hal_uart.o [5]
  .text              ro code   0x800'1908    0x1c  U32ToDbl.o [11]
  .text              ro code   0x800'1924   0x1aa  DblMul.o [11]
  .text              ro code   0x800'1ace     0x2  stm32f1xx_hal_uart.o [5]
  .text              ro code   0x800'1ad0     0x8  xlocale_c.o [10]
  .text              ro code   0x800'1ad8   0x274  I64DivMod.o [12]
  .text              ro code   0x800'1d4c     0x6  abort.o [10]
  .text              ro code   0x800'1d52     0x2  stm32f1xx_hal_uart.o [5]
  .text              ro code   0x800'1d54     0x2  I64DivZer.o [12]
  .text              ro code   0x800'1d56     0x2  stm32f1xx_hal_uart.o [5]
  .text              ro code   0x800'1d58    0x14  exit.o [13]
  .text              ro code   0x800'1d6c    0x2c  iarttio.o [13]
  .text              ro code   0x800'1d98     0x8  XShttio.o [10]
  .text              ro code   0x800'1da0   0xa54  tasks.o [4]
  .text              ro code   0x800'27f4   0x278  heap_4.o [4]
  .text              ro code   0x800'2a6c    0x66  ABImemset.o [12]
  .text              ro code   0x800'2ad2    0x9a  list.o [4]
  .text              ro code   0x800'2b6c   0x1d0  port.o [4]
  .text              ro code   0x800'2d3c   0x1b8  cmsis_os2.o [4]
  .text              ro code   0x800'2ef4   0x478  timers.o [4]
  .text              ro code   0x800'336c    0x34  port.o [4]
  CODE               ro code   0x800'33a0    0x88  portasm.o [4]
  .text              ro code   0x800'3428   0x770  queue.o [4]
  .text              ro code   0x800'3b98   0x860  spi_nor_time_series.o [6]
  .text              ro code   0x800'43f8   0x18c  debugApp.o [6]
  .text              ro code   0x800'4584    0x1a  U32ToFlt.o [11]
  .rodata            const     0x800'459e     0x1  xlocale_c.o [10]
  .text              ro code   0x800'45a0   0x118  FltDiv.o [11]
  .text              ro code   0x800'46b8    0x28  FltCmpLe.o [11]
  .text              ro code   0x800'46e0    0x48  FltToDbl.o [11]
  .text              ro code   0x800'4728    0x3c  vsnprint.o [10]
  .text              ro code   0x800'4764   0x7b8  stm32f1xx_hal_spi.o [5]
  .text              ro code   0x800'4f1c    0xcc  spi.o [2]
  .text              ro code   0x800'4fe8     0xc  stm32f1xx_hal.o [5]
  .text              ro code   0x800'4ff4    0xc2  main.o [2]
  .text              ro code   0x800'50b6    0x14  memset.o [12]
  .text              ro code   0x800'50cc   0x2a0  stm32f1xx_hal_gpio.o [5]
  .text              ro code   0x800'536c    0x24  stm32f1xx_hal.o [5]
  .text              ro code   0x800'5390    0x68  gpio.o [2]
  .text              ro code   0x800'53f8    0x4c  dma.o [2]
  .text              ro code   0x800'5444   0x2d4  usart.o [2]
  .text              ro code   0x800'5718    0x74  freertos.o [2]
  .text              ro code   0x800'578c    0x28  stm32f1xx_hal.o [5]
  .text              ro code   0x800'57b4   0x660  stm32f1xx_hal_rcc.o [5]
  .text              ro code   0x800'5e14   0x154  stm32f1xx_hal_cortex.o [5]
  .text              ro code   0x800'5f68    0x4c  stm32f1xx_hal.o [5]
  .text              ro code   0x800'5fb4    0x54  stm32f1xx_hal_msp.o [2]
  .text              ro code   0x800'6008   0x5b4  stm32f1xx_hal_uart.o [5]
  .text              ro code   0x800'65bc   0x66c  stm32f1xx_hal_dma.o [5]
  .text              ro code   0x800'6c28   0x274  userRtos.o [6]
  .text              ro code   0x800'6e9c    0x70  animal.o [6]
  .text              ro code   0x800'6f0c   0x190  print.o [6]
  .text              ro code   0x800'709c    0x44  lsd.o [7]
  .text              ro code   0x800'70e0    0x44  led.o [7]
  .text              ro code   0x800'7124    0xe0  mktime64.o [10]
  .text              ro code   0x800'7204    0x70  strncpy.o [12]
  .text              ro code   0x800'7274    0xcc  xDaysTo64.o [10]
  .text              ro code   0x800'7340   0x15c  xttotm64.o [10]
  .text              ro code   0x800'749c     0x4  xTzoff_nop.o [10]
  .text              ro code   0x800'74a0     0x6  xisdst_nop64.o [10]
  .text              ro code   0x800'74a8   0x238  W25Q128_flash.o [6]
  .text              ro code   0x800'76e0    0xc8  structAndFuc.o [6]
  .text              ro code   0x800'77a8    0x18  strcpy.o [12]
  .text              ro code   0x800'77c0    0x78  stm32f1xx_it.o [2]
  .text              ro code   0x800'7838    0x18  stm32f1xx_hal.o [5]
  .rodata            const     0x800'7850    0x38  spi_nor_time_series.o [6]
  .text              ro code   0x800'7888    0x38  zero_init3.o [12]
  .rodata            const     0x800'78c0    0x30  spi_nor_time_series.o [6]
  .rodata            const     0x800'78f0    0x2c  spi_nor_time_series.o [6]
  .rodata            const     0x800'791c    0x2c  userRtos.o [6]
  .rodata            const     0x800'7948    0x28  spi_nor_time_series.o [6]
  .rodata            const     0x800'7970    0x28  spi_nor_time_series.o [6]
  .text              ro code   0x800'7998    0x28  data_init.o [12]
  .rodata            const     0x800'79c0    0x24  freertos.o [2]
  .rodata            const     0x800'79e4    0x24  freertos.o [2]
  .iar.init_table    const     0x800'7a08    0x20  - Linker created -
  .rodata            const     0x800'7a28    0x20  spi_nor_time_series.o [6]
  .text              ro code   0x800'7a48    0x1e  cmain.o [12]
  .text              ro code   0x800'7a66     0x4  low_level_init.o [10]
  .text              ro code   0x800'7a6a     0x4  exit.o [10]
  .text              ro code   0x800'7a70     0xa  cexit.o [12]
  .rodata            const     0x800'7a7c    0x1c  spi_nor_time_series.o [6]
  .rodata            const     0x800'7a98    0x1c  spi_nor_time_series.o [6]
  .rodata            const     0x800'7ab4    0x1c  spi_nor_time_series.o [6]
  .rodata            const     0x800'7ad0    0x1c  structAndFuc.o [6]
  .text              ro code   0x800'7aec    0x1c  cstartup_M.o [12]
  .rodata            const     0x800'7b08    0x18  animal.o [6]
  .rodata            const     0x800'7b20    0x18  spi_nor_time_series.o [6]
  .rodata            const     0x800'7b38    0x18  spi_nor_time_series.o [6]
  .rodata            const     0x800'7b50    0x14  animal.o [6]
  .rodata            const     0x800'7b64    0x14  animal.o [6]
  .rodata            const     0x800'7b78    0x14  animal.o [6]
  .rodata            const     0x800'7b8c    0x14  freertos.o [2]
  .rodata            const     0x800'7ba0    0x14  spi_nor_time_series.o [6]
  .rodata            const     0x800'7bb4    0x14  spi_nor_time_series.o [6]
  .rodata            const     0x800'7bc8    0x14  spi_nor_time_series.o [6]
  .rodata            const     0x800'7bdc    0x14  spi_nor_time_series.o [6]
  .rodata            const     0x800'7bf0    0x14  spi_nor_time_series.o [6]
  .rodata            const     0x800'7c04    0x14  structAndFuc.o [6]
  .rodata            const     0x800'7c18    0x14  userRtos.o [6]
  .rodata            const     0x800'7c2c    0x10  print.o [6]
  .rodata            const     0x800'7c3c    0x10  spi_nor_time_series.o [6]
  .rodata            const     0x800'7c4c    0x10  stm32f1xx_hal_rcc.o [5]
  .rodata            const     0x800'7c5c    0x10  system_stm32f1xx.o [1]
  .rodata            const     0x800'7c6c    0x10  userRtos.o [6]
  .rodata            const     0x800'7c7c    0x10  userRtos.o [6]
  .rodata            const     0x800'7c8c    0x10  userRtos.o [6]
  .text              ro code   0x800'7c9c    0x10  startup_stm32f103xe.o [3]
  .rodata            const     0x800'7cac     0xc  freertos.o [2]
  .rodata            const     0x800'7cb8     0xc  freertos.o [2]
  .rodata            const     0x800'7cc4     0xc  led.o [7]
  .rodata            const     0x800'7cd0     0xc  lsd.o [7]
  .rodata            const     0x800'7cdc     0xc  print.o [6]
  .rodata            const     0x800'7ce8     0xc  print.o [6]
  .rodata            const     0x800'7cf4     0xc  userRtos.o [6]
  .rodata            const     0x800'7d00     0x8  animal.o [6]
  .rodata            const     0x800'7d08     0x8  animal.o [6]
  .rodata            const     0x800'7d10     0x8  led.o [7]
  .rodata            const     0x800'7d18     0x8  lsd.o [7]
  .rodata            const     0x800'7d20     0x8  main.o [2]
  .rodata            const     0x800'7d28     0x8  main.o [2]
  .rodata            const     0x800'7d30     0x8  print.o [6]
  .rodata            const     0x800'7d38     0x8  print.o [6]
  .rodata            const     0x800'7d40     0x8  print.o [6]
  .rodata            const     0x800'7d48     0x8  print.o [6]
  .rodata            const     0x800'7d50     0x8  print.o [6]
  .rodata            const     0x800'7d58     0x8  system_stm32f1xx.o [1]
  .rodata            const     0x800'7d60     0x8  tasks.o [4]
  .rodata            const     0x800'7d68     0x8  timers.o [4]
  .rodata            const     0x800'7d70     0x8  timers.o [4]
  .rodata            const     0x800'7d78     0x8  userRtos.o [6]
  .rodata            const     0x800'7d80     0x8  userRtos.o [6]
  .rodata            const     0x800'7d88     0x4  heap_4.o [4]
  .rodata            const     0x800'7d8c     0x4  port.o [4]
  .rodata            const     0x800'7d90     0x4  print.o [6]
  .rodata            const     0x800'7d94     0x4  print.o [6]
  .rodata            const     0x800'7d98     0x4  W25Q128_flash.o [6]
  .rodata            const     0x800'7d9c     0x4  W25Q128_flash.o [6]
  .rodata            const     0x800'7da0     0x4  W25Q128_flash.o [6]
  .rodata            const     0x800'7da4     0x4  W25Q128_flash.o [6]
  .rodata            const     0x800'7da8     0x4  W25Q128_flash.o [6]
  .text              ro code   0x800'7dac     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7db0     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7db4     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7db8     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7dbc     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7dc0     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7dc4     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7dc8     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7dcc     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7dd0     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7dd4     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7dd8     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7ddc     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7de0     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7de4     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7de8     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7dec     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7df0     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7df4     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7df8     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7dfc     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e00     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e04     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e08     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e0c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e10     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e14     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e18     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e1c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e20     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e24     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e28     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e2c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e30     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e34     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e38     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e3c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e40     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e44     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e48     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e4c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e50     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e54     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e58     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e5c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e60     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e64     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e68     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e6c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e70     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e74     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e78     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e7c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e80     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'7e84     0x2  system_stm32f1xx.o [1]
  .rodata            const     0x800'7e86     0x0  zero_init3.o [12]
  .rodata            const     0x800'7e86     0x0  packbits_init_single.o [12]
  Initializer bytes  const     0x800'7e86    0x84  <for P2-1>
                             - 0x800'7f0a  0x7dda

"P2", part 1 of 3:                           0xb1
  P2-1                        0x2000'0000    0xb1  <Init block>
    .data            inited   0x2000'0000     0xc  led.o [7]
    .data            inited   0x2000'000c     0xc  lsd.o [7]
    .data            inited   0x2000'0018     0x4  port.o [4]
    .data            inited   0x2000'001c     0x4  spi_nor_time_series.o [6]
    .data            inited   0x2000'0020     0x4  spi_nor_time_series.o [6]
    .data            inited   0x2000'0024     0x4  spi_nor_time_series.o [6]
    .data            inited   0x2000'0028     0x4  stm32f1xx_hal.o [5]
    .data            inited   0x2000'002c    0x24  structAndFuc.o [6]
    .data            inited   0x2000'0050     0x4  system_stm32f1xx.o [1]
    .data            inited   0x2000'0054    0x18  W25Q128_flash.o [6]
    .data            inited   0x2000'006c     0x4  W25Q128_flash.o [6]
    .data            inited   0x2000'0070    0x38  xlocale_c.o [10]
    .data            inited   0x2000'00a8     0x8  XShttio.o [10]
    .data            inited   0x2000'00b0     0x1  stm32f1xx_hal.o [5]
                            - 0x2000'00b1    0xb1

"P2", part 2 of 3:                         0xcc88
  .bss               zero     0x2000'00b4  0x7800  heap_4.o [4]
  .bss               zero     0x2000'78b4  0x3000  spi_nor_time_series.o [6]
  .bss               zero     0x2000'a8b4   0x808  debugApp.o [6]
  .bss               zero     0x2000'b0bc   0x600  userRtos.o [6]
  .bss               zero     0x2000'b6bc   0x460  tasks.o [4]
  .bss               zero     0x2000'bb1c   0x400  cmsis_os2.o [4]
  .bss               zero     0x2000'bf1c   0x400  debugApp.o [6]
  .bss               zero     0x2000'c31c   0x200  cmsis_os2.o [4]
  .bss               zero     0x2000'c51c   0x200  debugApp.o [6]
  .bss               zero     0x2000'c71c    0xa0  timers.o [4]
  .bss               zero     0x2000'c7bc    0x74  usart.o [2]
  .bss               zero     0x2000'c830    0x74  usart.o [2]
  .bss               zero     0x2000'c8a4    0x74  usart.o [2]
  .bss               zero     0x2000'c918    0x6c  cmsis_os2.o [4]
  .bss               zero     0x2000'c984    0x6c  cmsis_os2.o [4]
  .bss               zero     0x2000'c9f0    0x58  spi.o [2]
  .bss               zero     0x2000'ca48    0x50  timers.o [4]
  .bss               zero     0x2000'ca98    0x44  usart.o [2]
  .bss               zero     0x2000'cadc    0x44  usart.o [2]
  .bss               zero     0x2000'cb20    0x44  usart.o [2]
  .bss               zero     0x2000'cb64    0x40  queue.o [4]
  .bss               zero     0x2000'cba4    0x2c  xttotm64.o [10]
  .bss               zero     0x2000'cbd0    0x18  structAndFuc.o [6]
  .bss               zero     0x2000'cbe8    0x14  tasks.o [4]
  .bss               zero     0x2000'cbfc    0x14  tasks.o [4]
  .bss               zero     0x2000'cc10    0x14  tasks.o [4]
  .bss               zero     0x2000'cc24    0x14  tasks.o [4]
  .bss               zero     0x2000'cc38    0x14  tasks.o [4]
  .bss               zero     0x2000'cc4c    0x14  timers.o [4]
  .bss               zero     0x2000'cc60    0x14  timers.o [4]
  .bss               zero     0x2000'cc74    0x14  userRtos.o [6]
  .bss               zero     0x2000'cc88    0x14  userRtos.o [6]
  .bss               zero     0x2000'cc9c    0x10  debugApp.o [6]
  .bss               zero     0x2000'ccac     0xc  dataSerialization.o [6]
  .bss               zero     0x2000'ccb8     0x8  heap_4.o [4]
  .bss               zero     0x2000'ccc0     0x4  cmsis_os2.o [4]
  .bss               zero     0x2000'ccc4     0x4  freertos.o [2]
  .bss               zero     0x2000'ccc8     0x4  freertos.o [2]
  .bss               zero     0x2000'cccc     0x4  heap_4.o [4]
  .bss               zero     0x2000'ccd0     0x4  heap_4.o [4]
  .bss               zero     0x2000'ccd4     0x4  heap_4.o [4]
  .bss               zero     0x2000'ccd8     0x4  heap_4.o [4]
  .bss               zero     0x2000'ccdc     0x4  port.o [4]
  .bss               zero     0x2000'cce0     0x4  stm32f1xx_hal.o [5]
  .bss               zero     0x2000'cce4     0x4  tasks.o [4]
  .bss               zero     0x2000'cce8     0x4  tasks.o [4]
  .bss               zero     0x2000'ccec     0x4  tasks.o [4]
  .bss               zero     0x2000'ccf0     0x4  tasks.o [4]
  .bss               zero     0x2000'ccf4     0x4  tasks.o [4]
  .bss               zero     0x2000'ccf8     0x4  tasks.o [4]
  .bss               zero     0x2000'ccfc     0x4  tasks.o [4]
  .bss               zero     0x2000'cd00     0x4  tasks.o [4]
  .bss               zero     0x2000'cd04     0x4  tasks.o [4]
  .bss               zero     0x2000'cd08     0x4  tasks.o [4]
  .bss               zero     0x2000'cd0c     0x4  tasks.o [4]
  .bss               zero     0x2000'cd10     0x4  tasks.o [4]
  .bss               zero     0x2000'cd14     0x4  tasks.o [4]
  .bss               zero     0x2000'cd18     0x4  tasks.o [4]
  .bss               zero     0x2000'cd1c     0x4  tasks.o [4]
  .bss               zero     0x2000'cd20     0x4  timers.o [4]
  .bss               zero     0x2000'cd24     0x4  timers.o [4]
  .bss               zero     0x2000'cd28     0x4  timers.o [4]
  .bss               zero     0x2000'cd2c     0x4  timers.o [4]
  .bss               zero     0x2000'cd30     0x4  timers.o [4]
  .bss               zero     0x2000'cd34     0x4  xfail_s.o [10]
  .bss               zero     0x2000'cd38     0x2  userRtos.o [6]
  .bss               zero     0x2000'cd3a     0x1  port.o [4]
                            - 0x2000'cd3b  0xcc87

"P2", part 3 of 3:                         0x1000
  CSTACK                      0x2000'cd40  0x1000  <Block>
    CSTACK           uninit   0x2000'cd40  0x1000  <Block tail>
                            - 0x2000'dd40  0x1000

Unused ranges:

         From           To      Size
         ----           --      ----
   0x800'7f0a   0x807'ffff  0x7'80f6
  0x2000'00b1  0x2000'00b3       0x3
  0x2000'cd3c  0x2000'cd3f       0x4
  0x2000'dd40  0x2000'ffff    0x22c0


*******************************************************************************
*** INIT TABLE
***

          Address      Size
          -------      ----
Zero (__iar_zero_init3)
    1 destination range, total size 0xcc87:
          0x2000'00b4  0xcc87

Copy/packbits (__iar_packbits_init_single3)
    1 source range, total size 0x84 (74% of destination):
           0x800'7e86    0x84
    1 destination range, total size 0xb1:
          0x2000'0000    0xb1



*******************************************************************************
*** MODULE SUMMARY
***

    Module                  ro code  ro data  rw data
    ------                  -------  -------  -------
command line/config:
    -------------------------------------------------
    Total:

D:\routine\stm32f1_software\software_All_example\compileFile\Obj\CMSIS_6603591812247902717.dir: [1]
    system_stm32f1xx.o            2       27        4
    -------------------------------------------------
    Total:                        2       27        4

D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir: [2]
    dma.o                        76
    freertos.o                  116      116        8
    gpio.o                      104
    main.o                      194       16
    spi.o                       204                88
    stm32f1xx_hal_msp.o          84
    stm32f1xx_it.o              120
    usart.o                     724               552
    -------------------------------------------------
    Total:                    1'622      132      648

D:\routine\stm32f1_software\software_All_example\compileFile\Obj\EWARM_18443280873093131863.dir: [3]
    startup_stm32f103xe.o       536
    -------------------------------------------------
    Total:                      536

D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir: [4]
    cmsis_os2.o                 440             1'756
    heap_4.o                    632        4   30'744
    list.o                      154
    port.o                      516        7        9
    portasm.o                   136
    queue.o                   1'904                64
    tasks.o                   2'644        8    1'280
    timers.o                  1'144       16      300
    -------------------------------------------------
    Total:                    7'570       35   34'153

D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir: [5]
    stm32f1xx_hal.o             188        4        9
    stm32f1xx_hal_cortex.o      340
    stm32f1xx_hal_dma.o       1'644
    stm32f1xx_hal_gpio.o        672
    stm32f1xx_hal_rcc.o       1'632       18
    stm32f1xx_hal_spi.o       1'976
    stm32f1xx_hal_uart.o      1'478
    -------------------------------------------------
    Total:                    7'930       22        9

D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir: [6]
    W25Q128_flash.o             568       41       28
    animal.o                    112      100
    dataSerialization.o                            12
    debugApp.o                  396             3'608
    print.o                     400       88
    spi_nor_time_series.o     2'144      517   12'300
    structAndFuc.o              200       75       60
    userRtos.o                  628      140    1'578
    -------------------------------------------------
    Total:                    4'448      961   17'586

D:\routine\stm32f1_software\software_All_example\compileFile\Obj\light_fn_18089876452921840385.dir: [7]
    led.o                        68       28       12
    lsd.o                        68       29       12
    -------------------------------------------------
    Total:                      136       57       24

D:\routine\stm32f1_software\software_All_example\compileFile\Obj\mid_1628371642073116930.dir: [8]
    -------------------------------------------------
    Total:

D:\routine\stm32f1_software\software_All_example\compileFile\Obj\sfud_6715763213700909193.dir: [9]
    -------------------------------------------------
    Total:

dl7M_tlf.a: [10]
    XShttio.o                     8        6        8
    abort.o                       6
    exit.o                        4
    low_level_init.o              4
    mktime64.o                  224
    vsnprint.o                   60
    xDaysTo64.o                 204
    xTzoff_nop.o                  4
    xfail_s.o                    64                 4
    xisdst_nop64.o                6
    xlocale_c.o                   8       45       56
    xprintffull_nomb.o        3'786
    xsnprout.o                   26
    xttotm64.o                  348                44
    -------------------------------------------------
    Total:                    4'752       51      112

m7M_tl.a: [11]
    DblAddSub.o                 590
    DblCmpGe.o                   52
    DblCmpLe.o                   52
    DblDiv.o                    582
    DblMul.o                    426
    DblToS32.o                   54
    DblToU32.o                   34
    FltCmpLe.o                   40
    FltDiv.o                    280
    FltToDbl.o                   72
    S32ToDbl.o                   34
    U32ToDbl.o                   28
    U32ToFlt.o                   26
    frexp.o                     112
    ldexp.o                     314
    -------------------------------------------------
    Total:                    2'696

rt7M_tl.a: [12]
    ABImemcpy.o                 166
    ABImemset.o                 102
    I64DivMod.o                 628
    I64DivZer.o                   2
    cexit.o                      10
    cmain.o                      30
    cstartup_M.o                 28
    data_init.o                  40
    memchr.o                     88
    memset.o                     20
    packbits_init_single.o       58
    strchr.o                     22
    strcpy.o                     24
    strlen.o                     54
    strncpy.o                   112
    zero_init3.o                 56
    -------------------------------------------------
    Total:                    1'440

shb_l.a: [13]
    exit.o                       20
    iarttio.o                    44
    -------------------------------------------------
    Total:                       64

    Gaps                          7        2
    Linker created                        32    4'096
-----------------------------------------------------
    Grand Total:             31'203    1'319   56'632


*******************************************************************************
*** ENTRY LIST
***

Entry                       Address    Size  Type      Object
-----                       -------    ----  ----      ------
.iar.init_table$$Base    0x800'7a08           --   Gb  - Linker created -
.iar.init_table$$Limit   0x800'7a28           --   Gb  - Linker created -
?main                    0x800'7a49          Code  Gb  cmain.o [12]
ADC1_2_IRQHandler        0x800'7de9          Code  Wk  startup_stm32f103xe.o [3]
ADC3_IRQHandler          0x800'7e51          Code  Wk  startup_stm32f103xe.o [3]
AHBPrescTable            0x800'7c5c    0x10  Data  Gb  system_stm32f1xx.o [1]
APBPrescTable            0x800'7d58     0x8  Data  Gb  system_stm32f1xx.o [1]
AnimalTestTask           0x800'6ec5    0x30  Code  Gb  animal.o [6]
BusFault_Handler         0x800'77c7     0x2  Code  Gb  stm32f1xx_it.o [2]
CAN1_RX1_IRQHandler      0x800'7df5          Code  Wk  startup_stm32f103xe.o [3]
CAN1_SCE_IRQHandler      0x800'7df9          Code  Wk  startup_stm32f103xe.o [3]
CSTACK$$Base            0x2000'cd40           --   Gb  - Linker created -
CSTACK$$Limit           0x2000'dd40           --   Gb  - Linker created -
Cat_eat                  0x800'6ead    0x18  Code  Gb  animal.o [6]
DMA1_Channel1_IRQHandler
                         0x800'7dd9          Code  Wk  startup_stm32f103xe.o [3]
DMA1_Channel2_IRQHandler
                         0x800'7ddd          Code  Wk  startup_stm32f103xe.o [3]
DMA1_Channel3_IRQHandler
                         0x800'77e1     0xa  Code  Gb  stm32f1xx_it.o [2]
DMA1_Channel4_IRQHandler
                         0x800'7de1          Code  Wk  startup_stm32f103xe.o [3]
DMA1_Channel5_IRQHandler
                         0x800'77eb     0xa  Code  Gb  stm32f1xx_it.o [2]
DMA1_Channel6_IRQHandler
                         0x800'77f5     0xa  Code  Gb  stm32f1xx_it.o [2]
DMA1_Channel7_IRQHandler
                         0x800'7de5          Code  Wk  startup_stm32f103xe.o [3]
DMA2_Channel1_IRQHandler
                         0x800'7e75          Code  Wk  startup_stm32f103xe.o [3]
DMA2_Channel2_IRQHandler
                         0x800'7e79          Code  Wk  startup_stm32f103xe.o [3]
DMA2_Channel3_IRQHandler
                         0x800'7e7d          Code  Wk  startup_stm32f103xe.o [3]
DMA2_Channel4_5_IRQHandler
                         0x800'7e81          Code  Wk  startup_stm32f103xe.o [3]
DebugITSendBuf           0x800'4435    0x7a  Code  Gb  debugApp.o [6]
DebugMon_Handler         0x800'77cb     0x2  Code  Gb  stm32f1xx_it.o [2]
DebugUartCtrl           0x2000'cc9c    0x10  Data  Gb  debugApp.o [6]
DebugUartEvent           0x800'44af    0x88  Code  Gb  debugApp.o [6]
DebugUartInit            0x800'43f9    0x3c  Code  Gb  debugApp.o [6]
Dog_eat                  0x800'6e9d    0x10  Code  Gb  animal.o [6]
EXTI0_IRQHandler         0x800'7dc5          Code  Wk  startup_stm32f103xe.o [3]
EXTI15_10_IRQHandler     0x800'7e35          Code  Wk  startup_stm32f103xe.o [3]
EXTI1_IRQHandler         0x800'7dc9          Code  Wk  startup_stm32f103xe.o [3]
EXTI2_IRQHandler         0x800'7dcd          Code  Wk  startup_stm32f103xe.o [3]
EXTI3_IRQHandler         0x800'7dd1          Code  Wk  startup_stm32f103xe.o [3]
EXTI4_IRQHandler         0x800'7dd5          Code  Wk  startup_stm32f103xe.o [3]
EXTI9_5_IRQHandler       0x800'7dfd          Code  Wk  startup_stm32f103xe.o [3]
Error_Handler            0x800'50b3     0x4  Code  Gb  main.o [2]
FLASH_IRQHandler         0x800'7dbd          Code  Wk  startup_stm32f103xe.o [3]
FSMC_IRQHandler          0x800'7e55          Code  Wk  startup_stm32f103xe.o [3]
HAL_DMA_Abort            0x800'6647    0x4e  Code  Gb  stm32f1xx_hal_dma.o [5]
HAL_DMA_Abort_IT         0x800'6695   0x1be  Code  Gb  stm32f1xx_hal_dma.o [5]
HAL_DMA_IRQHandler       0x800'6899   0x358  Code  Gb  stm32f1xx_hal_dma.o [5]
HAL_DMA_Init             0x800'65bd    0x8a  Code  Gb  stm32f1xx_hal_dma.o [5]
HAL_Delay                0x800'578d    0x28  Code  Wk  stm32f1xx_hal.o [5]
HAL_GPIO_Init            0x800'50cd   0x23c  Code  Gb  stm32f1xx_hal_gpio.o [5]
HAL_GPIO_WritePin        0x800'5309    0x1a  Code  Gb  stm32f1xx_hal_gpio.o [5]
HAL_GetTick              0x800'4fe9     0xc  Code  Wk  stm32f1xx_hal.o [5]
HAL_IncTick              0x800'7839    0x18  Code  Wk  stm32f1xx_hal.o [5]
HAL_Init                 0x800'536d    0x20  Code  Gb  stm32f1xx_hal.o [5]
HAL_InitTick             0x800'5f69    0x4c  Code  Wk  stm32f1xx_hal.o [5]
HAL_MspInit              0x800'5fb5    0x54  Code  Gb  stm32f1xx_hal_msp.o [2]
HAL_NVIC_EnableIRQ       0x800'5f2d     0xe  Code  Gb  stm32f1xx_hal_cortex.o [5]
HAL_NVIC_SetPriority     0x800'5f03    0x2a  Code  Gb  stm32f1xx_hal_cortex.o [5]
HAL_NVIC_SetPriorityGrouping
                         0x800'5ef7     0xc  Code  Gb  stm32f1xx_hal_cortex.o [5]
HAL_RCC_ClockConfig      0x800'5bb5   0x14e  Code  Gb  stm32f1xx_hal_rcc.o [5]
HAL_RCC_GetHCLKFreq      0x800'5d73     0x6  Code  Gb  stm32f1xx_hal_rcc.o [5]
HAL_RCC_GetPCLK1Freq     0x800'5d79    0x16  Code  Gb  stm32f1xx_hal_rcc.o [5]
HAL_RCC_GetPCLK2Freq     0x800'5d8f    0x16  Code  Gb  stm32f1xx_hal_rcc.o [5]
HAL_RCC_GetSysClockFreq
                         0x800'5d03    0x70  Code  Gb  stm32f1xx_hal_rcc.o [5]
HAL_RCC_OscConfig        0x800'57b5   0x400  Code  Gb  stm32f1xx_hal_rcc.o [5]
HAL_SPI_Init             0x800'4765    0xc4  Code  Gb  stm32f1xx_hal_spi.o [5]
HAL_SPI_MspInit          0x800'4f63    0x72  Code  Gb  spi.o [2]
HAL_SPI_Receive          0x800'49df   0x17c  Code  Gb  stm32f1xx_hal_spi.o [5]
HAL_SPI_Transmit         0x800'4829   0x1b6  Code  Gb  stm32f1xx_hal_spi.o [5]
HAL_SPI_TransmitReceive
                         0x800'4b5b   0x23c  Code  Gb  stm32f1xx_hal_spi.o [5]
HAL_SYSTICK_Config       0x800'5f3b     0xc  Code  Gb  stm32f1xx_hal_cortex.o [5]
HAL_UARTEx_RxEventCallback
                         0x800'1d57     0x2  Code  Wk  stm32f1xx_hal_uart.o [5]
HAL_UART_AbortCpltCallback
                         0x800'1907     0x2  Code  Wk  stm32f1xx_hal_uart.o [5]
HAL_UART_AbortReceiveCpltCallback
                         0x800'1d53     0x2  Code  Wk  stm32f1xx_hal_uart.o [5]
HAL_UART_AbortTransmitCpltCallback
                         0x800'1acf     0x2  Code  Wk  stm32f1xx_hal_uart.o [5]
HAL_UART_ErrorCallback   0x800'18e3     0x2  Code  Wk  stm32f1xx_hal_uart.o [5]
HAL_UART_IRQHandler      0x800'6095   0x232  Code  Gb  stm32f1xx_hal_uart.o [5]
HAL_UART_Init            0x800'6009    0x88  Code  Gb  stm32f1xx_hal_uart.o [5]
HAL_UART_MspInit         0x800'54e1   0x1f8  Code  Gb  usart.o [2]
HAL_UART_RxCpltCallback
                         0x800'144b     0x2  Code  Wk  stm32f1xx_hal_uart.o [5]
HAL_UART_RxHalfCpltCallback
                         0x800'169b     0x2  Code  Wk  stm32f1xx_hal_uart.o [5]
HAL_UART_TxCpltCallback
                         0x800'13ef     0x2  Code  Wk  stm32f1xx_hal_uart.o [5]
HAL_UART_TxHalfCpltCallback
                         0x800'1427     0x2  Code  Wk  stm32f1xx_hal_uart.o [5]
HardFault_Handler        0x800'77c3     0x2  Code  Gb  stm32f1xx_it.o [2]
I2C1_ER_IRQHandler       0x800'7e21          Code  Wk  startup_stm32f103xe.o [3]
I2C1_EV_IRQHandler       0x800'7e1d          Code  Wk  startup_stm32f103xe.o [3]
I2C2_ER_IRQHandler       0x800'7e29          Code  Wk  startup_stm32f103xe.o [3]
I2C2_EV_IRQHandler       0x800'7e25          Code  Wk  startup_stm32f103xe.o [3]
Idle_Stack              0x2000'c31c   0x200  Data  Lc  cmsis_os2.o [4]
Idle_TCB                0x2000'c918    0x6c  Data  Lc  cmsis_os2.o [4]
KernelState             0x2000'ccc0     0x4  Data  Lc  cmsis_os2.o [4]
ListInit                 0x800'6c45    0x20  Code  Lc  userRtos.o [6]
ListItemInit             0x800'6c65     0x6  Code  Lc  userRtos.o [6]
ListItemInsertEnd        0x800'6c6b    0x36  Code  Lc  userRtos.o [6]
ListItemInsertRemove     0x800'6ccd    0x20  Code  Lc  userRtos.o [6]
ListItemInsertValue      0x800'6ca1    0x2c  Code  Lc  userRtos.o [6]
ListTask                 0x800'6d77    0xea  Code  Gb  userRtos.o [6]
ListTaskHandle          0x2000'ccc8     0x4  Data  Gb  freertos.o [2]
ListTask_attributes      0x800'79e4    0x24  Data  Gb  freertos.o [2]
ListTestTask             0x800'6ced    0x8a  Code  Lc  userRtos.o [6]
MX_DMA_Init              0x800'53f9    0x4c  Code  Gb  dma.o [2]
MX_FREERTOS_Init         0x800'5719    0x22  Code  Gb  freertos.o [2]
MX_GPIO_Init             0x800'5391    0x68  Code  Gb  gpio.o [2]
MX_SPI2_Init             0x800'4f1d    0x46  Code  Gb  spi.o [2]
MX_USART1_UART_Init      0x800'5445    0x34  Code  Gb  usart.o [2]
MX_USART2_UART_Init      0x800'5479    0x34  Code  Gb  usart.o [2]
MX_USART3_UART_Init      0x800'54ad    0x34  Code  Gb  usart.o [2]
MemManage_Handler        0x800'77c5     0x2  Code  Gb  stm32f1xx_it.o [2]
NMI_Handler              0x800'77c1     0x2  Code  Gb  stm32f1xx_it.o [2]
NVIC_EncodePriority      0x800'5e87    0x40  Code  Lc  stm32f1xx_hal_cortex.o [5]
PVD_IRQHandler           0x800'7db1          Code  Wk  startup_stm32f103xe.o [3]
PendSV_Handler           0x800'33a1          Code  Gb  portasm.o [4]
RCC_Delay                0x800'5da5    0x26  Code  Lc  stm32f1xx_hal_rcc.o [5]
RCC_IRQHandler           0x800'7dc1          Code  Wk  startup_stm32f103xe.o [3]
RTC_Alarm_IRQHandler     0x800'7e39          Code  Wk  startup_stm32f103xe.o [3]
RTC_IRQHandler           0x800'7db9          Code  Wk  startup_stm32f103xe.o [3]
Region$$Table$$Base      0x800'7a08           --   Gb  - Linker created -
Region$$Table$$Limit     0x800'7a28           --   Gb  - Linker created -
Reset_Handler            0x800'7c9d          Code  Wk  startup_stm32f103xe.o [3]
SDIO_IRQHandler          0x800'7e59          Code  Wk  startup_stm32f103xe.o [3]
SPI1_IRQHandler          0x800'7e2d          Code  Wk  startup_stm32f103xe.o [3]
SPI2_IRQHandler          0x800'7e31          Code  Wk  startup_stm32f103xe.o [3]
SPI3_IRQHandler          0x800'7e61          Code  Wk  startup_stm32f103xe.o [3]
SPI_EndRxTransaction     0x800'4e71    0x7c  Code  Lc  stm32f1xx_hal_spi.o [5]
SPI_EndRxTxTransaction   0x800'4eed    0x2a  Code  Lc  stm32f1xx_hal_spi.o [5]
SPI_WaitFlagStateUntilTimeout
                         0x800'4d97    0xda  Code  Lc  stm32f1xx_hal_spi.o [5]
SVC_Handler              0x800'33e7          Code  Gb  portasm.o [4]
StartDefaultTask         0x800'573d    0x36  Code  Gb  freertos.o [2]
SysTick_Config           0x800'5ec7    0x30  Code  Lc  stm32f1xx_hal_cortex.o [5]
SysTick_Handler          0x800'77cd    0x14  Code  Gb  stm32f1xx_it.o [2]
SystemClock_Config       0x800'5045    0x6e  Code  Gb  main.o [2]
SystemCoreClock         0x2000'0050     0x4  Data  Gb  system_stm32f1xx.o [1]
SystemInit               0x800'7e85     0x2  Code  Gb  system_stm32f1xx.o [1]
TAMPER_IRQHandler        0x800'7db5          Code  Wk  startup_stm32f103xe.o [3]
TIM1_BRK_IRQHandler      0x800'7e01          Code  Wk  startup_stm32f103xe.o [3]
TIM1_CC_IRQHandler       0x800'7e0d          Code  Wk  startup_stm32f103xe.o [3]
TIM1_TRG_COM_IRQHandler
                         0x800'7e09          Code  Wk  startup_stm32f103xe.o [3]
TIM1_UP_IRQHandler       0x800'7e05          Code  Wk  startup_stm32f103xe.o [3]
TIM2_IRQHandler          0x800'7e11          Code  Wk  startup_stm32f103xe.o [3]
TIM3_IRQHandler          0x800'7e15          Code  Wk  startup_stm32f103xe.o [3]
TIM4_IRQHandler          0x800'7e19          Code  Wk  startup_stm32f103xe.o [3]
TIM5_IRQHandler          0x800'7e5d          Code  Wk  startup_stm32f103xe.o [3]
TIM6_IRQHandler          0x800'7e6d          Code  Wk  startup_stm32f103xe.o [3]
TIM7_IRQHandler          0x800'7e71          Code  Wk  startup_stm32f103xe.o [3]
TIM8_BRK_IRQHandler      0x800'7e41          Code  Wk  startup_stm32f103xe.o [3]
TIM8_CC_IRQHandler       0x800'7e4d          Code  Wk  startup_stm32f103xe.o [3]
TIM8_TRG_COM_IRQHandler
                         0x800'7e49          Code  Wk  startup_stm32f103xe.o [3]
TIM8_UP_IRQHandler       0x800'7e45          Code  Wk  startup_stm32f103xe.o [3]
Timer_Stack             0x2000'bb1c   0x400  Data  Lc  cmsis_os2.o [4]
Timer_TCB               0x2000'c984    0x6c  Data  Lc  cmsis_os2.o [4]
UART4_IRQHandler         0x800'7e65          Code  Wk  startup_stm32f103xe.o [3]
UART5_IRQHandler         0x800'7e69          Code  Wk  startup_stm32f103xe.o [3]
UART_DMAAbortOnError     0x800'6339    0x16  Code  Lc  stm32f1xx_hal_uart.o [5]
UART_EndRxTransfer       0x800'62ff    0x36  Code  Lc  stm32f1xx_hal_uart.o [5]
UART_EndTransmit_IT      0x800'63b7    0x20  Code  Lc  stm32f1xx_hal_uart.o [5]
UART_InitCallbacksToDefault
                         0x800'62c7    0x38  Code  Gb  stm32f1xx_hal_uart.o [5]
UART_Receive_IT          0x800'63d7    0xe2  Code  Lc  stm32f1xx_hal_uart.o [5]
UART_SetConfig           0x800'64b9    0xd8  Code  Lc  stm32f1xx_hal_uart.o [5]
UART_Transmit_IT         0x800'6351    0x66  Code  Lc  stm32f1xx_hal_uart.o [5]
USART1_IRQHandler        0x800'77ff     0xa  Code  Gb  stm32f1xx_it.o [2]
USART2_IRQHandler        0x800'7809     0xe  Code  Gb  stm32f1xx_it.o [2]
USART3_IRQHandler        0x800'7817     0xa  Code  Gb  stm32f1xx_it.o [2]
USBWakeUp_IRQHandler     0x800'7e3d          Code  Wk  startup_stm32f103xe.o [3]
USB_HP_CAN1_TX_IRQHandler
                         0x800'7ded          Code  Wk  startup_stm32f103xe.o [3]
USB_LP_CAN1_RX0_IRQHandler
                         0x800'7df1          Code  Wk  startup_stm32f103xe.o [3]
UsageFault_Handler       0x800'77c9     0x2  Code  Gb  stm32f1xx_it.o [2]
W25Q                    0x2000'0054    0x18  Data  Lc  W25Q128_flash.o [6]
W25Q_CS_High             0x800'74b9    0x10  Code  Lc  W25Q128_flash.o [6]
W25Q_CS_Low              0x800'74a9    0x10  Code  Lc  W25Q128_flash.o [6]
W25Q_EraseBlock64K       0x800'7681    0x44  Code  Lc  W25Q128_flash.o [6]
W25Q_EraseSector         0x800'763d    0x44  Code  Lc  W25Q128_flash.o [6]
W25Q_Flash              0x2000'006c     0x4  Data  Gb  W25Q128_flash.o [6]
W25Q_Init                0x800'7565    0x1e  Code  Lc  W25Q128_flash.o [6]
W25Q_PageProgram         0x800'75d5    0x68  Code  Lc  W25Q128_flash.o [6]
W25Q_ReadData            0x800'7583    0x52  Code  Lc  W25Q128_flash.o [6]
W25Q_ReadID              0x800'7523    0x42  Code  Lc  W25Q128_flash.o [6]
W25Q_WaitBusy            0x800'74e9    0x3a  Code  Lc  W25Q128_flash.o [6]
W25Q_WriteEnable         0x800'74c9    0x20  Code  Lc  W25Q128_flash.o [6]
WWDG_IRQHandler          0x800'7dad          Code  Wk  startup_stm32f103xe.o [3]
_LC                      0x800'0131     0x6  Code  Lc  xprintffull_nomb.o [10]
_LitobFullNoMb           0x800'0e19   0x114  Code  Lc  xprintffull_nomb.o [10]
_Locale_lconv           0x2000'0070    0x38  Data  Lc  xlocale_c.o [10]
_PrintfFullNoMb          0x800'0137   0xcb2  Code  Gb  xprintffull_nomb.o [10]
_PutcharsFullNoMb        0x800'0f85    0x2e  Code  Lc  xprintffull_nomb.o [10]
_SNProut                 0x800'104b    0x1a  Code  Gb  xsnprout.o [10]
_Ttotm_ts               0x2000'cba4    0x2c  Data  Lc  xttotm64.o [10]
_Tzoff                   0x800'749d     0x4  Code  Gb  xTzoff_nop.o [10]
__NVIC_EnableIRQ         0x800'5e3d    0x1e  Code  Lc  stm32f1xx_hal_cortex.o [5]
__NVIC_GetPriorityGrouping
                         0x800'5e33     0xa  Code  Lc  stm32f1xx_hal_cortex.o [5]
__NVIC_SetPriority       0x800'5e5b    0x2c  Code  Lc  stm32f1xx_hal_cortex.o [5]
__NVIC_SetPriorityGrouping
                         0x800'5e15    0x1e  Code  Lc  stm32f1xx_hal_cortex.o [5]
__aeabi_cdcmple          0x800'124d          Code  Gb  DblCmpLe.o [11]
__aeabi_cdrcmple         0x800'1281          Code  Gb  DblCmpGe.o [11]
__aeabi_cfcmple          0x800'46b9          Code  Gb  FltCmpLe.o [11]
__aeabi_d2iz             0x800'13f1          Code  Gb  DblToS32.o [11]
__aeabi_d2uiz            0x800'18e5          Code  Gb  DblToU32.o [11]
__aeabi_ddiv             0x800'169d          Code  Gb  DblDiv.o [11]
__aeabi_dmul             0x800'1925          Code  Gb  DblMul.o [11]
__aeabi_dsub             0x800'15b9          Code  Gb  DblAddSub.o [11]
__aeabi_f2d              0x800'46e1          Code  Gb  FltToDbl.o [11]
__aeabi_fdiv             0x800'45a1          Code  Gb  FltDiv.o [11]
__aeabi_i2d              0x800'1429          Code  Gb  S32ToDbl.o [11]
__aeabi_ldiv0            0x800'1d55          Code  Gb  I64DivZer.o [12]
__aeabi_ldivmod          0x800'1ad9          Code  Gb  I64DivMod.o [12]
__aeabi_memcpy           0x800'1135          Code  Gb  ABImemcpy.o [12]
__aeabi_memcpy4          0x800'1155          Code  Gb  ABImemcpy.o [12]
__aeabi_memcpy8          0x800'1155          Code  Gb  ABImemcpy.o [12]
__aeabi_memset           0x800'2a6d          Code  Gb  ABImemset.o [12]
__aeabi_ui2d             0x800'1909          Code  Gb  U32ToDbl.o [11]
__aeabi_ui2f             0x800'4585          Code  Gb  U32ToFlt.o [11]
__aeabi_uldivmod         0x800'1b1d          Code  Gb  I64DivMod.o [12]
__cmain                  0x800'7a49          Code  Gb  cmain.o [12]
__exit                   0x800'1d59    0x14  Code  Gb  exit.o [13]
__iar_Daysto64           0x800'72a9    0x66  Code  Gb  xDaysTo64.o [10]
__iar_Fail_s             0x800'1065    0x1c  Code  Gb  xfail_s.o [10]
__iar_Isdst64            0x800'74a1     0x6  Code  Gb  xisdst_nop64.o [10]
__iar_Memchr             0x800'10dd          Code  Gb  memchr.o [12]
__iar_Memset             0x800'2a6d          Code  Gb  ABImemset.o [12]
__iar_Memset_word        0x800'2a75          Code  Gb  ABImemset.o [12]
__iar_MonTab64           0x800'7275    0x34  Code  Gb  xDaysTo64.o [10]
__iar_Strchr             0x800'1035          Code  Gb  strchr.o [12]
__iar_Ttotm64            0x800'7341   0x15c  Code  Gb  xttotm64.o [10]
__iar_close_ttio         0x800'1d6d    0x2c  Code  Gb  iarttio.o [13]
__iar_data_init3         0x800'7999    0x28  Code  Gb  data_init.o [12]
__iar_frexp              0x800'11e9          Code  Gb  frexp.o [11]
__iar_frexp64            0x800'11dd          Code  Gb  frexp.o [11]
__iar_frexpl             0x800'11e9          Code  Gb  frexp.o [11]
__iar_ldexp64            0x800'12b5          Code  Gb  ldexp.o [11]
__iar_lookup_ttioh       0x800'1d99     0x8  Code  Gb  XShttio.o [10]
__iar_packbits_init_single3
                         0x800'0ffb    0x3a  Code  Gb  packbits_init_single.o [12]
__iar_program_start      0x800'7aed          Code  Gb  cstartup_M.o [12]
__iar_scalbln64          0x800'12b5          Code  Gb  ldexp.o [11]
__iar_scalbn64           0x800'12b5          Code  Gb  ldexp.o [11]
__iar_ttio_handles      0x2000'00a8     0x8  Data  Lc  XShttio.o [10]
__iar_zero_init3         0x800'7889    0x38  Code  Gb  zero_init3.o [12]
__low_level_init         0x800'7a67     0x4  Code  Gb  low_level_init.o [10]
__mktime64               0x800'7125    0xe0  Code  Gb  mktime64.o [10]
__vector_table           0x800'0000          Data  Gb  startup_stm32f103xe.o [3]
_call_main               0x800'7a55          Code  Gb  cmain.o [12]
_exit                    0x800'7a71          Code  Gb  cexit.o [12]
abort                    0x800'1d4d     0x6  Code  Gb  abort.o [10]
align_up                 0x800'3bd1    0x10  Code  Lc  spi_nor_time_series.o [6]
color_printer            0x800'7d94     0x4  Data  Lc  print.o [6]
color_printer_SetColor   0x800'6fc3    0x12  Code  Gb  print.o [6]
color_printer_delete     0x800'6fb1    0x12  Code  Gb  print.o [6]
color_printer_new        0x800'6f6d    0x44  Code  Lc  print.o [6]
color_printer_print      0x800'6f49    0x24  Code  Lc  print.o [6]
crc16_ccitt              0x800'3b99    0x38  Code  Lc  spi_nor_time_series.o [6]
dataStruct              0x2000'ccac     0xc  Data  Lc  dataSerialization.o [6]
data_write_ptr          0x2000'0020     0x4  Data  Lc  spi_nor_time_series.o [6]
debugRecvRingBuf        0x2000'c51c   0x200  Data  Gb  debugApp.o [6]
debugSendRingBuf        0x2000'bf1c   0x400  Data  Gb  debugApp.o [6]
debug_buffer            0x2000'a8b4   0x808  Data  Gb  debugApp.o [6]
defaultTaskHandle       0x2000'ccc4     0x4  Data  Gb  freertos.o [2]
defaultTask_attributes   0x800'79c0    0x24  Data  Gb  freertos.o [2]
delayList               0x2000'cc74    0x14  Data  Lc  userRtos.o [6]
do_gc_once               0x800'411d   0x22c  Code  Lc  spi_nor_time_series.o [6]
exit                     0x800'7a6b     0x4  Code  Gb  exit.o [10]
frexp                    0x800'11dd          Code  Gb  frexp.o [11]
frexpl                   0x800'11dd          Code  Gb  frexp.o [11]
fs_init                  0x800'43ad    0x14  Code  Gb  spi_nor_time_series.o [6]
fs_load_by_timestamp     0x800'43e9    0x10  Code  Gb  spi_nor_time_series.o [6]
fs_read_by_timestamp     0x800'3eaf    0xae  Code  Lc  spi_nor_time_series.o [6]
fs_store_timestamped     0x800'43c1    0x28  Code  Gb  spi_nor_time_series.o [6]
fs_write_record          0x800'3d7d   0x132  Code  Lc  spi_nor_time_series.o [6]
global_seq_counter      0x2000'001c     0x4  Data  Lc  spi_nor_time_series.o [6]
hdma_usart1_rx          0x2000'ca98    0x44  Data  Gb  usart.o [2]
hdma_usart2_rx          0x2000'cadc    0x44  Data  Gb  usart.o [2]
hdma_usart3_rx          0x2000'cb20    0x44  Data  Gb  usart.o [2]
hspi2                   0x2000'c9f0    0x58  Data  Gb  spi.o [2]
huart1                  0x2000'c7bc    0x74  Data  Gb  usart.o [2]
huart2                  0x2000'c830    0x74  Data  Gb  usart.o [2]
huart3                  0x2000'c8a4    0x74  Data  Gb  usart.o [2]
index_append             0x800'3ce7    0x96  Code  Lc  spi_nor_time_series.o [6]
index_write_ptr         0x2000'0024     0x4  Data  Lc  spi_nor_time_series.o [6]
ldexp                    0x800'12b5          Code  Gb  ldexp.o [11]
ldexpl                   0x800'12b5          Code  Gb  ldexp.o [11]
led_get_state            0x800'7105     0x4  Code  Gb  led.o [7]
led_init                 0x800'7109     0xe  Code  Gb  led.o [7]
led_interface           0x2000'0000     0xc  Data  Lc  led.o [7]
led_off                  0x800'70f3    0x12  Code  Gb  led.o [7]
led_on                   0x800'70e1    0x12  Code  Gb  led.o [7]
light_off                0x800'6c37     0xe  Code  Lc  userRtos.o [6]
light_on                 0x800'6c29     0xe  Code  Lc  userRtos.o [6]
listStaticMaxNum        0x2000'cd38     0x2  Data  Lc  userRtos.o [6]
lmos                     0x800'7310    0x18  Data  Lc  xDaysTo64.o [10]
localeconv               0x800'1ad1     0x4  Code  Gb  xlocale_c.o [10]
lsd_get_state            0x800'70c1     0x4  Code  Gb  lsd.o [7]
lsd_init                 0x800'70c5     0xe  Code  Gb  lsd.o [7]
lsd_interface           0x2000'000c     0xc  Data  Lc  lsd.o [7]
lsd_off                  0x800'70af    0x12  Code  Gb  lsd.o [7]
lsd_on                   0x800'709d    0x12  Code  Gb  lsd.o [7]
main                     0x800'4ff5    0x48  Code  Gb  main.o [2]
memchr                   0x800'10dd          Code  Gb  memchr.o [12]
memset                   0x800'50b7    0x14  Code  Gb  memset.o [12]
mktime                   0x800'7125    0xe0  Code  Gb  mktime64.o [10]
mos                      0x800'7328    0x18  Data  Lc  xDaysTo64.o [10]
osKernelInitialize       0x800'2d3d    0x3c  Code  Gb  cmsis_os2.o [4]
osKernelStart            0x800'2d79    0x42  Code  Gb  cmsis_os2.o [4]
osThreadNew              0x800'2dbb   0x106  Code  Gb  cmsis_os2.o [4]
pcInterruptPriorityRegisters
                         0x800'7d8c     0x4  Data  Lc  port.o [4]
plain_printer_delete     0x800'6f3d     0xc  Code  Lc  print.o [6]
plain_printer_new        0x800'6f1f    0x1e  Code  Gb  print.o [6]
plain_printer_print      0x800'6f0d    0x12  Code  Lc  print.o [6]
printer_interface        0x800'7d90     0x4  Data  Lc  print.o [6]
prvAddCurrentTaskToDelayedList
                         0x800'2755    0x6c  Code  Lc  tasks.o [4]
prvAddNewTaskToReadyList
                         0x800'1f8b    0x9e  Code  Lc  tasks.o [4]
prvCheckForValidListAndQueue
                         0x800'32f1    0x4c  Code  Lc  timers.o [4]
prvCheckForValidListAndQueue{1}{2}{3}{4}::ucStaticTimerQueueStorage
                        0x2000'c71c    0xa0  Data  Lc  timers.o [4]
prvCheckForValidListAndQueue{1}{2}{3}{4}::xStaticTimerQueue
                        0x2000'ca48    0x50  Data  Lc  timers.o [4]
prvCheckTasksWaitingTermination
                         0x800'25d3    0x3e  Code  Lc  tasks.o [4]
prvCopyDataFromQueue     0x800'3a45    0x36  Code  Lc  queue.o [4]
prvCopyDataToQueue       0x800'39a9    0x9c  Code  Lc  queue.o [4]
prvDeleteTCB             0x800'2615    0x44  Code  Lc  tasks.o [4]
prvGetNextExpireTime     0x800'30cf    0x2a  Code  Lc  timers.o [4]
prvHeapInit              0x800'298b    0x66  Code  Lc  heap_4.o [4]
prvIdleTask              0x800'2555    0x24  Code  Lc  tasks.o [4]
prvInitialiseNewQueue    0x800'357d    0x36  Code  Lc  queue.o [4]
prvInitialiseNewTask     0x800'1eab    0xe0  Code  Lc  tasks.o [4]
prvInitialiseTaskLists   0x800'257d    0x56  Code  Lc  tasks.o [4]
prvInsertBlockIntoFreeList
                         0x800'29f1    0x5e  Code  Lc  heap_4.o [4]
prvInsertTimerInActiveList
                         0x800'3121    0x50  Code  Lc  timers.o [4]
prvIsQueueEmpty          0x800'3ae9    0x1c  Code  Lc  queue.o [4]
prvIsQueueFull           0x800'3b05    0x1e  Code  Lc  queue.o [4]
prvProcessExpiredTimer   0x800'2fe3    0x5c  Code  Lc  timers.o [4]
prvProcessReceivedCommands
                         0x800'3171    0xf4  Code  Lc  timers.o [4]
prvProcessTimerOrBlockTask
                         0x800'305b    0x74  Code  Lc  timers.o [4]
prvResetNextTaskUnblockTime
                         0x800'2659    0x26  Code  Lc  tasks.o [4]
prvSampleTimeNow         0x800'30f9    0x28  Code  Lc  timers.o [4]
prvSampleTimeNow::xLastTime
                        0x2000'cd30     0x4  Data  Lc  timers.o [4]
prvSwitchTimerLists      0x800'3265    0x8c  Code  Lc  timers.o [4]
prvTaskExitError         0x800'2b8d    0x2a  Code  Lc  port.o [4]
prvTimerTask             0x800'3041    0x1a  Code  Lc  timers.o [4]
prvUnlockQueue           0x800'3a7b    0x6e  Code  Lc  queue.o [4]
pvPortMalloc             0x800'27f5   0x122  Code  Gb  heap_4.o [4]
pxCurrentTCB            0x2000'cce4     0x4  Data  Gb  tasks.o [4]
pxCurrentTimerList      0x2000'cd20     0x4  Data  Lc  timers.o [4]
pxDelayedTaskList       0x2000'cce8     0x4  Data  Lc  tasks.o [4]
pxEnd                   0x2000'cccc     0x4  Data  Lc  heap_4.o [4]
pxOverflowDelayedTaskList
                        0x2000'ccec     0x4  Data  Lc  tasks.o [4]
pxOverflowTimerList     0x2000'cd24     0x4  Data  Lc  timers.o [4]
pxPortInitialiseStack    0x800'2b6d    0x20  Code  Gb  port.o [4]
pxReadyTasksLists       0x2000'b6bc   0x460  Data  Lc  tasks.o [4]
readyList               0x2000'cc88    0x14  Data  Lc  userRtos.o [6]
rebuild_index            0x800'3f5d   0x1c0  Code  Lc  spi_nor_time_series.o [6]
scalbln                  0x800'12b5          Code  Gb  ldexp.o [11]
scalblnl                 0x800'12b5          Code  Gb  ldexp.o [11]
scalbn                   0x800'12b5          Code  Gb  ldexp.o [11]
scalbnl                  0x800'12b5          Code  Gb  ldexp.o [11]
scale                    0x800'0f2d    0x46  Code  Lc  xprintffull_nomb.o [10]
sec_hand                0x2000'cd34     0x4  Data  Lc  xfail_s.o [10]
slot_map                0x2000'78b4  0x3000  Data  Lc  spi_nor_time_series.o [6]
slot_map_find_index      0x800'3bf9    0x4e  Code  Lc  spi_nor_time_series.o [6]
slot_map_get             0x800'3ca1    0x46  Code  Lc  spi_nor_time_series.o [6]
slot_map_init            0x800'3be1    0x18  Code  Lc  spi_nor_time_series.o [6]
slot_map_put             0x800'3c47    0x5a  Code  Lc  spi_nor_time_series.o [6]
strchr                   0x800'1035          Code  Gb  strchr.o [12]
strcpy                   0x800'77a9          Code  Gb  strcpy.o [12]
strlen                   0x800'10a5          Code  Gb  strlen.o [12]
strncpy                  0x800'7205          Code  Gb  strncpy.o [12]
stuPrivate              0x2000'cbd0    0x18  Data  Lc  structAndFuc.o [6]
student_createAndArgs    0x800'773b    0x42  Code  Lc  structAndFuc.o [6]
student_createDefault    0x800'772b    0x10  Code  Lc  structAndFuc.o [6]
student_delete           0x800'76e1    0x12  Code  Lc  structAndFuc.o [6]
student_f               0x2000'002c    0x24  Data  Gb  structAndFuc.o [6]
student_getAge           0x800'7707     0x6  Code  Lc  structAndFuc.o [6]
student_getName          0x800'7713     0x6  Code  Lc  structAndFuc.o [6]
student_print            0x800'76f3    0x14  Code  Lc  structAndFuc.o [6]
student_setAge           0x800'770d     0x6  Code  Lc  structAndFuc.o [6]
student_setName          0x800'7719    0x12  Code  Lc  structAndFuc.o [6]
test_print               0x800'6fd5    0x8c  Code  Gb  print.o [6]
ucHeap                  0x2000'00b4  0x7800  Data  Lc  heap_4.o [4]
ucMaxSysCallPriority    0x2000'cd3a     0x1  Data  Lc  port.o [4]
ulMaxPRIGROUPValue      0x2000'ccdc     0x4  Data  Lc  port.o [4]
userContainerBuf        0x2000'b0bc   0x600  Data  Gb  userRtos.o [6]
user_printf              0x800'4537    0x30  Code  Gb  debugApp.o [6]
uwTick                  0x2000'cce0     0x4  Data  Gb  stm32f1xx_hal.o [5]
uwTickFreq              0x2000'00b0     0x1  Data  Gb  stm32f1xx_hal.o [5]
uwTickPrio              0x2000'0028     0x4  Data  Gb  stm32f1xx_hal.o [5]
uxCriticalNesting       0x2000'0018     0x4  Data  Lc  port.o [4]
uxCurrentNumberOfTasks  0x2000'ccf4     0x4  Data  Lc  tasks.o [4]
uxDeletedTasksWaitingCleanUp
                        0x2000'ccf0     0x4  Data  Lc  tasks.o [4]
uxListRemove             0x800'2b45    0x28  Code  Gb  list.o [4]
uxPendedTicks           0x2000'cd04     0x4  Data  Lc  tasks.o [4]
uxSchedulerSuspended    0x2000'cd1c     0x4  Data  Lc  tasks.o [4]
uxTaskNumber            0x2000'cd10     0x4  Data  Lc  tasks.o [4]
uxTopReadyPriority      0x2000'ccfc     0x4  Data  Lc  tasks.o [4]
vApplicationGetIdleTaskMemory
                         0x800'2ec5     0xe  Code  Gb  cmsis_os2.o [4]
vApplicationGetTimerTaskMemory
                         0x800'2ed3    0x10  Code  Gb  cmsis_os2.o [4]
vListInitialise          0x800'2ad3    0x1e  Code  Gb  list.o [4]
vListInitialiseItem      0x800'2af1     0x6  Code  Gb  list.o [4]
vListInsert              0x800'2b0f    0x36  Code  Gb  list.o [4]
vListInsertEnd           0x800'2af7    0x18  Code  Gb  list.o [4]
vPortEnterCritical       0x800'2c45    0x34  Code  Gb  port.o [4]
vPortExitCritical        0x800'2c79    0x2c  Code  Gb  port.o [4]
vPortFree                0x800'2917    0x74  Code  Gb  heap_4.o [4]
vPortSetupTimerInterrupt
                         0x800'336d    0x34  Code  Wk  port.o [4]
vPortStartFirstTask      0x800'3407          Code  Gb  portasm.o [4]
vPortValidateInterruptPriority
                         0x800'2ccd    0x4e  Code  Gb  port.o [4]
vQueueAddToRegistry      0x800'3b23    0x26  Code  Gb  queue.o [4]
vQueueWaitForMessageRestricted
                         0x800'3b4d    0x4c  Code  Gb  queue.o [4]
vTaskDelay               0x800'2029    0x4e  Code  Gb  tasks.o [4]
vTaskInternalSetTimeOutState
                         0x800'24a1    0x12  Code  Gb  tasks.o [4]
vTaskMissedYield         0x800'2547     0xa  Code  Gb  tasks.o [4]
vTaskPlaceOnEventList    0x800'2389    0x34  Code  Gb  tasks.o [4]
vTaskPlaceOnEventListRestricted
                         0x800'23c9    0x40  Code  Gb  tasks.o [4]
vTaskStartScheduler      0x800'2077    0x9a  Code  Gb  tasks.o [4]
vTaskSuspendAll          0x800'2111     0xc  Code  Gb  tasks.o [4]
vTaskSwitchContext       0x800'2317    0x72  Code  Gb  tasks.o [4]
vsnprintf                0x800'4729    0x3c  Code  Gb  vsnprint.o [10]
xActiveTimerList1       0x2000'cc4c    0x14  Data  Lc  timers.o [4]
xActiveTimerList2       0x2000'cc60    0x14  Data  Lc  timers.o [4]
xBlockAllocatedBit      0x2000'ccd8     0x4  Data  Lc  heap_4.o [4]
xDelayedTaskList1       0x2000'cbe8    0x14  Data  Lc  tasks.o [4]
xDelayedTaskList2       0x2000'cbfc    0x14  Data  Lc  tasks.o [4]
xFreeBytesRemaining     0x2000'ccd0     0x4  Data  Lc  heap_4.o [4]
xHeapStructSize          0x800'7d88     0x4  Data  Lc  heap_4.o [4]
xIdleTaskHandle         0x2000'cd18     0x4  Data  Lc  tasks.o [4]
xMinimumEverFreeBytesRemaining
                        0x2000'ccd4     0x4  Data  Lc  heap_4.o [4]
xNextTaskUnblockTime    0x2000'cd14     0x4  Data  Lc  tasks.o [4]
xNumOfOverflows         0x2000'cd0c     0x4  Data  Lc  tasks.o [4]
xPendingReadyList       0x2000'cc10    0x14  Data  Lc  tasks.o [4]
xPortStartScheduler      0x800'2bb7    0x8e  Code  Gb  port.o [4]
xPortSysTickHandler      0x800'2ca5    0x28  Code  Gb  port.o [4]
xQueueGenericCreateStatic
                         0x800'34bd    0xc0  Code  Gb  queue.o [4]
xQueueGenericReset       0x800'3429    0x94  Code  Gb  queue.o [4]
xQueueGenericSend        0x800'35b3   0x1a6  Code  Gb  queue.o [4]
xQueueGenericSendFromISR
                         0x800'3759    0xe0  Code  Gb  queue.o [4]
xQueueReceive            0x800'3839   0x16a  Code  Gb  queue.o [4]
xQueueRegistry          0x2000'cb64    0x40  Data  Gb  queue.o [4]
xSchedulerRunning       0x2000'cd00     0x4  Data  Lc  tasks.o [4]
xStart                  0x2000'ccb8     0x8  Data  Lc  heap_4.o [4]
xSuspendedTaskList      0x2000'cc38    0x14  Data  Lc  tasks.o [4]
xTaskCheckForTimeOut     0x800'24b9    0x8e  Code  Gb  tasks.o [4]
xTaskCreate              0x800'1e39    0x72  Code  Gb  tasks.o [4]
xTaskCreateStatic        0x800'1da1    0x98  Code  Gb  tasks.o [4]
xTaskGetSchedulerState   0x800'267f    0x20  Code  Gb  tasks.o [4]
xTaskGetTickCount        0x800'2205     0x8  Code  Gb  tasks.o [4]
xTaskIncrementTick       0x800'220d   0x10a  Code  Gb  tasks.o [4]
xTaskPriorityDisinherit
                         0x800'269f    0x8e  Code  Gb  tasks.o [4]
xTaskRemoveFromEventList
                         0x800'2415    0x82  Code  Gb  tasks.o [4]
xTaskResumeAll           0x800'211d    0xe8  Code  Gb  tasks.o [4]
xTasksWaitingTermination
                        0x2000'cc24    0x14  Data  Lc  tasks.o [4]
xTickCount              0x2000'ccf8     0x4  Data  Lc  tasks.o [4]
xTimerCreateTimerTask    0x800'2ef5    0x6c  Code  Gb  timers.o [4]
xTimerGenericCommand     0x800'2f61    0x82  Code  Gb  timers.o [4]
xTimerQueue             0x2000'cd28     0x4  Data  Lc  timers.o [4]
xTimerTaskHandle        0x2000'cd2c     0x4  Data  Lc  timers.o [4]
xYieldPending           0x2000'cd08     0x4  Data  Lc  tasks.o [4]


[1] = D:\routine\stm32f1_software\software_All_example\compileFile\Obj\CMSIS_6603591812247902717.dir
[2] = D:\routine\stm32f1_software\software_All_example\compileFile\Obj\Core_13247989168731456611.dir
[3] = D:\routine\stm32f1_software\software_All_example\compileFile\Obj\EWARM_18443280873093131863.dir
[4] = D:\routine\stm32f1_software\software_All_example\compileFile\Obj\FreeRTOS_4809373609813369194.dir
[5] = D:\routine\stm32f1_software\software_All_example\compileFile\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir
[6] = D:\routine\stm32f1_software\software_All_example\compileFile\Obj\app_1052054763364153231.dir
[7] = D:\routine\stm32f1_software\software_All_example\compileFile\Obj\light_fn_18089876452921840385.dir
[8] = D:\routine\stm32f1_software\software_All_example\compileFile\Obj\mid_1628371642073116930.dir
[9] = D:\routine\stm32f1_software\software_All_example\compileFile\Obj\sfud_6715763213700909193.dir
[10] = dl7M_tlf.a
[11] = m7M_tl.a
[12] = rt7M_tl.a
[13] = shb_l.a

  31'203 bytes of readonly  code memory
   1'319 bytes of readonly  data memory
  56'632 bytes of readwrite data memory

Errors: none
Warnings: none
