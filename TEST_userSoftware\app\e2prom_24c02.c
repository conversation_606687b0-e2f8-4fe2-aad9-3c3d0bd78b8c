/*
 * @Author: li
 * @Date: 2025-08-17 19:44:42
 * @LastEditTime: 2025-08-17 20:49:17
 * @Description:
 */
#include "e2prom_24c02.h"
#include "string.h"

static I2C_HandleTypeDef *e2prom_i2c;

// 内部函数：等待写完成（ACK轮询）
static HAL_StatusTypeDef E2PROM_WaitStandby(void)
{
    uint32_t timeout = HAL_GetTick() + 10; // 最多等10ms
    while (HAL_GetTick() < timeout)
    {
        if (HAL_I2C_IsDeviceReady(e2prom_i2c, E2PROM_24C02_ADDR << 1, 1, 5) == HAL_OK)
            return HAL_OK;
    }
    return HAL_TIMEOUT;
}

int E2PROM_Init(I2C_HandleTypeDef *hi2c)
{
    if (hi2c == NULL)
        return -1;

    e2prom_i2c = hi2c;
    return 0;
}

HAL_StatusTypeDef E2PROM_WriteByte(uint16_t addr, uint8_t data)
{
    uint8_t buf[2];
    buf[0] = (uint8_t)(addr & 0xFF);
    buf[1] = data;

    HAL_StatusTypeDef status = HAL_I2C_Master_Transmit(e2prom_i2c,
                                    E2PROM_24C02_ADDR << 1,
                                    buf, 2, HAL_MAX_DELAY);

    if (status != HAL_OK) return status;

    return E2PROM_WaitStandby();
}

HAL_StatusTypeDef E2PROM_ReadByte(uint16_t addr, uint8_t *data)
{
    uint8_t addr_buf = (uint8_t)(addr & 0xFF);

    // 先写地址
    HAL_StatusTypeDef status = HAL_I2C_Master_Transmit(e2prom_i2c,
                                    E2PROM_24C02_ADDR << 1,
                                    &addr_buf, 1, HAL_MAX_DELAY);
    if (status != HAL_OK) return status;

    // 再读数据
    return HAL_I2C_Master_Receive(e2prom_i2c,
                                    E2PROM_24C02_ADDR << 1,
                                    data, 1, HAL_MAX_DELAY);
}

HAL_StatusTypeDef E2PROM_Write(uint16_t addr, uint8_t *data, uint16_t len)
{
    while (len > 0)
    {
        // 当前页剩余空间
        uint16_t page_space = E2PROM_PAGE_SIZE - (addr % E2PROM_PAGE_SIZE);
        uint16_t to_write = (len < page_space) ? len : page_space;

        uint8_t buf[E2PROM_PAGE_SIZE + 1]; // 地址+数据
        buf[0] = (uint8_t)(addr & 0xFF);
        memcpy(&buf[1], data, to_write);

        HAL_StatusTypeDef status = HAL_I2C_Master_Transmit(e2prom_i2c,
                                        E2PROM_24C02_ADDR << 1,
                                        buf, to_write + 1, HAL_MAX_DELAY);
        if (status != HAL_OK) return status;

        if (E2PROM_WaitStandby() != HAL_OK) return HAL_ERROR;

        addr  += to_write;
        data  += to_write;
        len   -= to_write;
    }
    return HAL_OK;
}

HAL_StatusTypeDef E2PROM_Read(uint16_t addr, uint8_t *data, uint16_t len)
{
    uint8_t addr_buf = (uint8_t)(addr & 0xFF);

    HAL_StatusTypeDef status = HAL_I2C_Master_Transmit(e2prom_i2c,
                                    E2PROM_24C02_ADDR << 1,
                                    &addr_buf, 1, HAL_MAX_DELAY);
    if (status != HAL_OK) return status;

    return HAL_I2C_Master_Receive(e2prom_i2c,
                                    E2PROM_24C02_ADDR << 1,
                                    data, len, HAL_MAX_DELAY);
}